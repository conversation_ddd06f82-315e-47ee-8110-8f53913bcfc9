#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
القسم الأول: المعلومات الشخصية ووصف المشروع
Section 1: Personal Information and Project Summary

معلومات صاحب المشروع والوصف التفصيلي للمشروع
"""

import tkinter as tk
from tkinter import ttk

class PersonalInfoSection:
    """قسم المعلومات الشخصية"""
    
    def __init__(self, parent, colors, fonts):
        self.parent = parent
        self.colors = colors
        self.fonts = fonts
        self.data = {}
        
        self.create_section()
        
    def create_section(self):
        """إنشاء محتوى القسم"""
        # === الحاوي الرئيسي ===
        main_container = tk.Frame(self.parent, bg=self.colors['light'])
        main_container.pack(fill="both", expand=True, padx=30, pady=30)
        
        # === رأس القسم ===
        self.create_header(main_container)
        
        # === منطقة المحتوى ===
        content_area = tk.Frame(main_container, bg=self.colors['white'], relief="solid", bd=1)
        content_area.pack(fill="both", expand=True, pady=(0, 30))
        
        # === المحتوى الداخلي ===
        inner_content = tk.Frame(content_area, bg=self.colors['white'])
        inner_content.pack(fill="both", expand=True, padx=40, pady=35)
        
        # === قسم المعلومات الشخصية ===
        self.create_personal_info_section(inner_content)
        
        # === قسم معلومات المشروع ===
        self.create_project_info_section(inner_content)
        
        # === قسم التمويل ===
        self.create_funding_section(inner_content)
        
    def create_header(self, parent):
        """إنشاء رأس القسم"""
        header_container = tk.Frame(parent, bg=self.colors['light'])
        header_container.pack(fill="x", pady=(0, 35))
        
        # إطار الرأس
        header_frame = tk.Frame(header_container, bg=self.colors['primary'], relief="solid", bd=2)
        header_frame.pack(fill="x")
        
        # محتوى الرأس
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(fill="x", padx=45, pady=30)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_content, 
                              text="👤 المعلومات الشخصية ووصف المشروع",
                              font=self.fonts['title'], 
                              bg=self.colors['primary'], 
                              fg=self.colors['white'],
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        # العنوان الفرعي
        subtitle_label = tk.Label(header_content,
                                 text="يرجى إدخال جميع المعلومات الشخصية ووصف المشروع بدقة واكتمال",
                                 font=self.fonts['normal'],
                                 bg=self.colors['primary'],
                                 fg=self.colors['gray_medium'],
                                 anchor="e", justify="right")
        subtitle_label.pack(anchor="e", fill="x", pady=(10, 0))
        
        # خط ذهبي
        gold_line = tk.Frame(header_container, bg=self.colors['accent'], height=4)
        gold_line.pack(fill="x")
        
    def create_personal_info_section(self, parent):
        """إنشاء قسم المعلومات الشخصية"""
        section = self.create_section_frame(parent, "👤 المعلومات الشخصية", self.colors['info'])
        
        # الحقول الشخصية
        personal_fields = [
            ("👤", "الاسم الكامل", "full_name", "أدخل الاسم الكامل باللغة العربية", self.colors['info']),
            ("🎂", "العمر بالسنوات", "age", "أدخل العمر بالأرقام فقط", self.colors['success']),
            ("🎓", "المؤهل العلمي", "education", "آخر مؤهل علمي حصلت عليه", self.colors['warning']),
            ("💼", "سنوات الخبرة", "experience", "عدد سنوات الخبرة في مجال العمل", self.colors['danger']),
            ("📱", "رقم الهاتف", "phone", "رقم الهاتف مع رمز البلد (+966)", self.colors['warning']),
            ("📧", "البريد الإلكتروني", "email", "عنوان البريد الإلكتروني الصحيح", self.colors['info'])
        ]
        
        self.personal_entries = {}
        
        for icon, label, key, placeholder, color in personal_fields:
            field_container = self.create_input_field(section, icon, label, placeholder, color)
            self.personal_entries[key] = field_container["entry"]
            
    def create_project_info_section(self, parent):
        """إنشاء قسم معلومات المشروع"""
        section = self.create_section_frame(parent, "🚀 معلومات المشروع التجاري", self.colors['info'])
        
        # اسم المشروع
        name_field = self.create_input_field(section, "🏢", "اسم المشروع التجاري", 
                                           "أدخل اسم المشروع كما سيظهر رسمياً", self.colors['info'])
        self.project_name_entry = name_field["entry"]
        
        # وصف المشروع
        desc_container = self.create_textarea_field(section, "📝", "وصف المشروع التفصيلي", 
                                                   "اكتب وصفاً شاملاً ومفصلاً عن مشروعك وأهدافه ورؤيتك المستقبلية", self.colors['info'])
        self.project_desc_text = desc_container["textarea"]
        
        # نوع المشروع
        type_field = self.create_input_field(section, "🏭", "نوع المشروع", 
                                           "مثال: تجاري، صناعي، خدمي، تقني", self.colors['info'])
        self.project_type_entry = type_field["entry"]
        
        # الموقع المقترح
        location_field = self.create_input_field(section, "📍", "الموقع المقترح للمشروع", 
                                                "المدينة والحي المقترح لتنفيذ المشروع", self.colors['info'])
        self.project_location_entry = location_field["entry"]
        
    def create_funding_section(self, parent):
        """إنشاء قسم التمويل"""
        section = self.create_section_frame(parent, "💰 خطة التمويل والاستثمار", self.colors['danger'])
        
        # نوع التمويل
        funding_options_container = self.create_options_field(section, "💳", "نوع التمويل المطلوب")
        
        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = [
            ("💼", "تمويل ذاتي", "ذاتي", self.colors['success']),
            ("🏦", "قرض بنكي", "قرض بنكي", self.colors['danger']),
            ("👥", "مستثمر خارجي", "مستثمر", self.colors['warning']),
            ("🔄", "تمويل مختلط", "مختلط", self.colors['info'])
        ]
        
        for icon, text, value, color in funding_options:
            self.create_radio_option(funding_options_container, icon, text, value, color)
        
        # مبلغ التمويل
        amount_field = self.create_input_field(section, "💵", "مبلغ التمويل المطلوب (ريال سعودي)", 
                                             "أدخل المبلغ المطلوب بالأرقام فقط", self.colors['danger'])
        self.funding_amount_entry = amount_field["entry"]
        
        # مصدر التمويل
        source_field = self.create_textarea_field(section, "🏛️", "مصدر التمويل المتوقع", 
                                                 "اذكر المصادر المحتملة للتمويل (بنوك، مستثمرين، برامج دعم حكومية)", self.colors['danger'])
        self.funding_source_text = source_field["textarea"]
        
    def create_section_frame(self, parent, title, color):
        """إنشاء إطار قسم"""
        # عنوان القسم
        section_title_frame = tk.Frame(parent, bg=self.colors['white'])
        section_title_frame.pack(fill="x", pady=(30, 25))
        
        title_label = tk.Label(section_title_frame, text=title, 
                              font=self.fonts['title'], bg=self.colors['white'], fg=color,
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        # خط تحت العنوان
        title_line = tk.Frame(section_title_frame, bg=color, height=3)
        title_line.pack(fill="x", pady=(8, 0))
        
        # إطار القسم
        section_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        section_frame.pack(fill="x", pady=(0, 25))
        
        # محتوى القسم
        section_content = tk.Frame(section_frame, bg=self.colors['white'])
        section_content.pack(fill="x", padx=30, pady=25)
        
        return section_content
        
    def create_input_field(self, parent, icon, label, placeholder, color):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_container = tk.Frame(parent, bg=self.colors['white'])
        field_container.pack(fill="x", pady=15)
        
        # تسمية الحقل
        label_frame = tk.Frame(field_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        # النص والأيقونة
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        # إطار الحقل
        input_frame = tk.Frame(field_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        input_frame.pack(fill="x")
        
        # حقل الإدخال
        entry = tk.Entry(input_frame, font=self.fonts['arabic'], relief="flat", bd=0,
                        bg=self.colors['gray_light'], fg=self.colors['text_primary'], 
                        insertbackground=color, justify="right")
        entry.pack(fill="x", padx=18, pady=15, ipady=8)
        
        # خط مؤشر
        indicator_line = tk.Frame(field_container, bg=self.colors['gray_medium'], height=3)
        indicator_line.pack(fill="x", pady=(5, 0))
        
        # تأثيرات التفاعل
        self.add_field_effects(entry, placeholder, indicator_line, color)
        
        return {"entry": entry, "indicator": indicator_line}
        
    def create_textarea_field(self, parent, icon, label, placeholder, color):
        """إنشاء منطقة نص"""
        # إطار المنطقة
        textarea_container = tk.Frame(parent, bg=self.colors['white'])
        textarea_container.pack(fill="x", pady=15)
        
        # تسمية المنطقة
        label_frame = tk.Frame(textarea_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        # إطار منطقة النص
        text_frame = tk.Frame(textarea_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        text_frame.pack(fill="both", expand=True)
        
        # منطقة النص
        textarea = tk.Text(text_frame, height=6, font=self.fonts['arabic'],
                          relief="flat", bd=0, bg=self.colors['gray_light'], fg=self.colors['text_primary'],
                          wrap=tk.WORD, insertbackground=color)
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=textarea.yview)
        textarea.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب RTL
        scrollbar.pack(side="left", fill="y")
        textarea.pack(side="right", fill="both", expand=True, padx=18, pady=15)
        
        # إضافة placeholder
        self.add_textarea_placeholder(textarea, placeholder)
        
        return {"textarea": textarea, "scrollbar": scrollbar}
        
    def create_options_field(self, parent, icon, label):
        """إنشاء قسم خيارات"""
        options_container = tk.Frame(parent, bg=self.colors['white'])
        options_container.pack(fill="x", pady=15)
        
        # تسمية الخيارات
        label_frame = tk.Frame(options_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 18))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=self.colors['text_primary']).pack(side="right")
        
        # إطار الخيارات
        options_frame = tk.Frame(options_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        options_frame.pack(fill="x")
        
        return options_frame
        
    def create_radio_option(self, parent, icon, text, value, color):
        """إنشاء خيار راديو"""
        option_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        option_frame.pack(fill="x", pady=6, padx=15)
        
        rb = tk.Radiobutton(option_frame, text=f"{icon} {text}", variable=self.funding_type,
                          value=value, bg=self.colors['gray_light'], font=self.fonts['normal'],
                          fg=color, selectcolor=self.colors['accent'], cursor="hand2", pady=12,
                          anchor="e", justify="right")
        rb.pack(anchor="e", fill="x", padx=25)
        
    def add_field_effects(self, entry, placeholder, indicator, color):
        """إضافة تأثيرات للحقول"""
        # إضافة placeholder
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")
        
        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg=self.colors['text_primary'], bg=self.colors['white'])
            indicator.config(bg=color)
            entry.master.config(relief="solid", bd=2, highlightbackground=color)
        
        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8", bg=self.colors['gray_light'])
            indicator.config(bg=self.colors['gray_medium'])
            entry.master.config(relief="solid", bd=1, highlightbackground=self.colors['gray_medium'])
        
        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)
        
    def add_textarea_placeholder(self, textarea, placeholder):
        """إضافة placeholder لمنطقة النص"""
        textarea.insert("1.0", placeholder)
        textarea.config(fg="#94a3b8")
        textarea.tag_configure("rtl", justify="right")
        textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_in(event):
            if textarea.get("1.0", "end-1c") == placeholder:
                textarea.delete("1.0", tk.END)
                textarea.config(fg=self.colors['text_primary'])
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_out(event):
            if textarea.get("1.0", "end-1c").strip() == "":
                textarea.insert("1.0", placeholder)
                textarea.config(fg="#94a3b8")
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        textarea.bind("<FocusIn>", on_focus_in)
        textarea.bind("<FocusOut>", on_focus_out)
        
    def get_data(self):
        """الحصول على بيانات القسم"""
        data = {}
        
        # البيانات الشخصية
        for key, entry in self.personal_entries.items():
            data[key] = entry.get()
            
        # معلومات المشروع
        data['project_name'] = self.project_name_entry.get()
        data['project_description'] = self.project_desc_text.get("1.0", "end-1c")
        data['project_type'] = self.project_type_entry.get()
        data['project_location'] = self.project_location_entry.get()
        
        # التمويل
        data['funding_type'] = self.funding_type.get()
        data['funding_amount'] = self.funding_amount_entry.get()
        data['funding_source'] = self.funding_source_text.get("1.0", "end-1c")
        
        return data
        
    def set_data(self, data):
        """تعيين بيانات القسم"""
        try:
            # تحميل البيانات الشخصية
            for key, entry in self.personal_entries.items():
                if key in data:
                    entry.delete(0, tk.END)
                    entry.insert(0, data[key])

            # تحميل معلومات المشروع
            if 'project_name' in data:
                self.project_name_entry.delete(0, tk.END)
                self.project_name_entry.insert(0, data['project_name'])

            if 'project_description' in data:
                self.project_desc_text.delete("1.0", tk.END)
                self.project_desc_text.insert("1.0", data['project_description'])

            if 'project_type' in data:
                self.project_type_entry.delete(0, tk.END)
                self.project_type_entry.insert(0, data['project_type'])

            if 'project_location' in data:
                self.project_location_entry.delete(0, tk.END)
                self.project_location_entry.insert(0, data['project_location'])

            # تحميل بيانات التمويل
            if 'funding_type' in data:
                self.funding_type.set(data['funding_type'])

            if 'funding_amount' in data:
                self.funding_amount_entry.delete(0, tk.END)
                self.funding_amount_entry.insert(0, data['funding_amount'])

            if 'funding_source' in data:
                self.funding_source_text.delete("1.0", tk.END)
                self.funding_source_text.insert("1.0", data['funding_source'])

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def clear_data(self):
        """مسح بيانات القسم"""
        try:
            # مسح البيانات الشخصية
            for entry in self.personal_entries.values():
                entry.delete(0, tk.END)

            # مسح معلومات المشروع
            self.project_name_entry.delete(0, tk.END)
            self.project_desc_text.delete("1.0", tk.END)
            self.project_type_entry.delete(0, tk.END)
            self.project_location_entry.delete(0, tk.END)

            # مسح بيانات التمويل
            self.funding_type.set("ذاتي")
            self.funding_amount_entry.delete(0, tk.END)
            self.funding_source_text.delete("1.0", tk.END)

        except Exception as e:
            print(f"خطأ في مسح البيانات: {e}")
