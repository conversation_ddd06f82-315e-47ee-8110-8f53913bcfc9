#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
القسم الأول: المعلومات الشخصية ووصف المشروع
Section 1: Personal Information and Project Summary

معلومات صاحب المشروع والوصف التفصيلي للمشروع
"""

import tkinter as tk
from tkinter import ttk

class PersonalInfoSection:
    """قسم المعلومات الشخصية"""
    
    def __init__(self, parent, colors, fonts):
        self.parent = parent
        self.colors = colors
        self.fonts = fonts
        self.data = {}
        
        self.create_section()
        
    def create_section(self):
        """إنشاء محتوى القسم"""
        # === الحاوي الرئيسي ===
        main_container = tk.Frame(self.parent, bg=self.colors['light'])
        main_container.pack(fill="both", expand=True, padx=30, pady=30)
        
        # === رأس القسم ===
        self.create_header(main_container)
        
        # === منطقة المحتوى ===
        content_area = tk.Frame(main_container, bg=self.colors['white'], relief="solid", bd=1)
        content_area.pack(fill="both", expand=True, pady=(0, 30))
        
        # === المحتوى الداخلي ===
        inner_content = tk.Frame(content_area, bg=self.colors['white'])
        inner_content.pack(fill="both", expand=True, padx=40, pady=35)
        
        # === قسم المعلومات الشخصية ===
        self.create_personal_info_section(inner_content)

        # === قسم وصف المشروع ===
        self.create_project_description_section(inner_content)

        # === قسم التمويل والتكاليف ===
        self.create_funding_costs_section(inner_content)

        # === قسم تفاصيل المشروع ===
        self.create_project_details_section(inner_content)
        
    def create_header(self, parent):
        """إنشاء رأس القسم"""
        header_container = tk.Frame(parent, bg=self.colors['light'])
        header_container.pack(fill="x", pady=(0, 35))
        
        # إطار الرأس
        header_frame = tk.Frame(header_container, bg=self.colors['primary'], relief="solid", bd=2)
        header_frame.pack(fill="x")
        
        # محتوى الرأس
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(fill="x", padx=45, pady=30)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_content, 
                              text="👤 المعلومات الشخصية ووصف المشروع",
                              font=self.fonts['title'], 
                              bg=self.colors['primary'], 
                              fg=self.colors['white'],
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        # العنوان الفرعي
        subtitle_label = tk.Label(header_content,
                                 text="يرجى إدخال جميع المعلومات الشخصية ووصف المشروع بدقة واكتمال",
                                 font=self.fonts['normal'],
                                 bg=self.colors['primary'],
                                 fg=self.colors['gray_medium'],
                                 anchor="e", justify="right")
        subtitle_label.pack(anchor="e", fill="x", pady=(10, 0))
        
        # خط ذهبي
        gold_line = tk.Frame(header_container, bg=self.colors['accent'], height=4)
        gold_line.pack(fill="x")
        
    def create_personal_info_section(self, parent):
        """إنشاء قسم المعلومات الشخصية المحدث"""
        section = self.create_section_frame(parent, "👤 المعلومات الشخصية", self.colors['info'])

        # الحقول الشخصية المحدثة
        personal_fields = [
            ("👤", "اسم صاحب المشروع", "owner_name", "أدخل الاسم الكامل لصاحب المشروع", self.colors['info']),
            ("🎂", "العمر", "age", "أدخل العمر بالسنوات", self.colors['success']),
            ("💍", "الحالة الاجتماعية", "marital_status", "أعزب / متزوج / مطلق / أرمل", self.colors['warning']),
            ("👨‍👩‍👧‍👦", "عدد أفراد الأسرة", "family_members", "العدد الإجمالي لأفراد الأسرة", self.colors['info']),
            ("🎓", "المؤهل العلمي", "education", "آخر مؤهل علمي حصلت عليه", self.colors['warning']),
            ("📱", "رقم الهاتف", "phone", "رقم الهاتف الأساسي مع رمز البلد", self.colors['success']),
            ("📞", "رقم هاتف معرف", "alt_phone", "رقم هاتف إضافي للتواصل", self.colors['info']),
            ("🏠", "مكان السكن", "residence", "المدينة والحي الذي تسكن فيه", self.colors['warning'])
        ]

        self.personal_entries = {}

        # إنشاء شبكة للحقول الشخصية
        fields_grid = tk.Frame(section, bg=self.colors['white'])
        fields_grid.pack(fill="both", expand=True, pady=15)

        # تقسيم الحقول إلى عمودين
        for i, (icon, label, key, placeholder, color) in enumerate(personal_fields):
            # تحديد العمود (0 للأيمن، 1 للأيسر)
            col = i % 2
            row = i // 2

            # إنشاء إطار للحقل
            field_frame = tk.Frame(fields_grid, bg=self.colors['white'])
            field_frame.grid(row=row, column=col, sticky="ew", padx=10, pady=8)

            # تكوين الشبكة للتوسع
            fields_grid.grid_columnconfigure(col, weight=1)

            # إنشاء الحقل
            field_container = self.create_compact_input_field(field_frame, icon, label, placeholder, color)
            self.personal_entries[key] = field_container["entry"]
            
    def create_project_description_section(self, parent):
        """إنشاء قسم وصف المشروع المحدث"""
        section = self.create_section_frame(parent, "🚀 وصف المشروع", self.colors['success'])

        # الحقول الأساسية للمشروع
        project_basic_fields = [
            ("🏢", "اسم المشروع", "project_name", "أدخل اسم المشروع كما سيظهر رسمياً", self.colors['success']),
            ("📍", "الموقع", "location", "المدينة والحي المقترح لتنفيذ المشروع", self.colors['info'])
        ]

        # إنشاء الحقول الأساسية
        for icon, label, key, placeholder, color in project_basic_fields:
            field_container = self.create_input_field(section, icon, label, placeholder, color)
            setattr(self, f"{key}_entry", field_container["entry"])

        # أهمية الفكرة
        importance_field = self.create_textarea_field(section, "💡", "أهمية الفكرة",
                                                     "اشرح أهمية فكرة مشروعك وما يميزها عن الأفكار الأخرى في السوق",
                                                     self.colors['success'])
        self.idea_importance_text = importance_field["textarea"]

        # المهارات المطلوبة
        skills_field = self.create_textarea_field(section, "🎯", "المهارات المطلوبة",
                                                 "اذكر المهارات والخبرات المطلوبة لتنفيذ هذا المشروع بنجاح",
                                                 self.colors['warning'])
        self.required_skills_text = skills_field["textarea"]

        # حاجة المجتمع للمشروع
        community_need_field = self.create_textarea_field(section, "🌍", "حاجة المجتمع للمشروع",
                                                         "وضح كيف يلبي مشروعك حاجة حقيقية في المجتمع وما المشكلة التي يحلها",
                                                         self.colors['info'])
        self.community_need_text = community_need_field["textarea"]
        
    def create_funding_costs_section(self, parent):
        """إنشاء قسم التمويل والتكاليف المحدث"""
        section = self.create_section_frame(parent, "💰 التمويل والتكاليف", self.colors['danger'])

        # شبكة التمويل والتكاليف
        funding_grid = tk.Frame(section, bg=self.colors['white'])
        funding_grid.pack(fill="both", expand=True, pady=15)

        # تكوين الشبكة
        funding_grid.grid_columnconfigure(0, weight=1)
        funding_grid.grid_columnconfigure(1, weight=1)

        # الحقول المالية
        financial_fields = [
            ("🎁", "قيمة المنحة", "grant_value", "قيمة المنحة المطلوبة بالريال السعودي", self.colors['success'], 0, 0),
            ("💼", "التمويل الذاتي", "self_funding", "المبلغ الذي ستساهم به من مالك الخاص", self.colors['info'], 0, 1),
            ("💰", "تكلفة المشروع الكلية", "total_cost", "إجمالي تكلفة المشروع بالريال السعودي", self.colors['danger'], 1, 0)
        ]

        # إنشاء الحقول المالية
        for icon, label, key, placeholder, color, row, col in financial_fields:
            field_frame = tk.Frame(funding_grid, bg=self.colors['white'])
            field_frame.grid(row=row, column=col, sticky="ew", padx=10, pady=8)

            field_container = self.create_compact_input_field(field_frame, icon, label, placeholder, color)
            setattr(self, f"{key}_entry", field_container["entry"])

    def create_project_details_section(self, parent):
        """إنشاء قسم تفاصيل المشروع"""
        section = self.create_section_frame(parent, "📋 تفاصيل المشروع", self.colors['warning'])

        # الترخيص
        license_container = self.create_options_field(section, "📜", "هل يحتاج المشروع إلى ترخيص؟")

        self.license_required = tk.StringVar(value="نعم")
        license_options = [
            ("✅", "نعم، يحتاج إلى ترخيص", "نعم", self.colors['danger']),
            ("❌", "لا، لا يحتاج إلى ترخيص", "لا", self.colors['success'])
        ]

        for icon, text, value, color in license_options:
            self.create_radio_option(license_container, icon, text, value, color)

        # الفئة المستهدفة
        target_group_container = self.create_options_field(section, "🎯", "الفئة المستهدفة")

        self.target_group = tk.StringVar(value="شباب")
        target_groups = [
            ("👶", "أطفال", "أطفال", self.colors['info']),
            ("👦", "شباب", "شباب", self.colors['success']),
            ("👨", "كبار السن", "كبار السن", self.colors['warning']),
            ("👥", "جميع الفئات", "جميع الفئات", self.colors['primary']),
            ("🔄", "أخرى", "أخرى", self.colors['secondary'])
        ]

        # إنشاء شبكة للفئات المستهدفة
        target_grid = tk.Frame(target_group_container, bg=self.colors['gray_light'])
        target_grid.pack(fill="x", padx=15, pady=10)

        for i, (icon, text, value, color) in enumerate(target_groups):
            row = i // 2
            col = i % 2

            option_frame = tk.Frame(target_grid, bg=self.colors['gray_light'])
            option_frame.grid(row=row, column=col, sticky="ew", padx=5, pady=3)
            target_grid.grid_columnconfigure(col, weight=1)

            rb = tk.Radiobutton(option_frame, text=f"{icon} {text}", variable=self.target_group,
                              value=value, bg=self.colors['gray_light'], font=self.fonts['normal'],
                              fg=color, selectcolor=self.colors['accent'], cursor="hand2", pady=8,
                              anchor="e", justify="right")
            rb.pack(anchor="e", fill="x", padx=10)

        # حقل أخرى للفئة المستهدفة
        other_target_field = self.create_input_field(section, "✏️", "إذا اخترت 'أخرى'، حدد الفئة المستهدفة",
                                                    "اكتب الفئة المستهدفة المحددة", self.colors['secondary'])
        self.other_target_entry = other_target_field["entry"]
        
    def create_section_frame(self, parent, title, color):
        """إنشاء إطار قسم"""
        # عنوان القسم
        section_title_frame = tk.Frame(parent, bg=self.colors['white'])
        section_title_frame.pack(fill="x", pady=(30, 25))
        
        title_label = tk.Label(section_title_frame, text=title, 
                              font=self.fonts['title'], bg=self.colors['white'], fg=color,
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        # خط تحت العنوان
        title_line = tk.Frame(section_title_frame, bg=color, height=3)
        title_line.pack(fill="x", pady=(8, 0))
        
        # إطار القسم
        section_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        section_frame.pack(fill="x", pady=(0, 25))
        
        # محتوى القسم
        section_content = tk.Frame(section_frame, bg=self.colors['white'])
        section_content.pack(fill="x", padx=30, pady=25)
        
        return section_content
        
    def create_input_field(self, parent, icon, label, placeholder, color):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_container = tk.Frame(parent, bg=self.colors['white'])
        field_container.pack(fill="x", pady=15)
        
        # تسمية الحقل
        label_frame = tk.Frame(field_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        # النص والأيقونة
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        # إطار الحقل
        input_frame = tk.Frame(field_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        input_frame.pack(fill="x")
        
        # حقل الإدخال
        entry = tk.Entry(input_frame, font=self.fonts['arabic'], relief="flat", bd=0,
                        bg=self.colors['gray_light'], fg=self.colors['text_primary'], 
                        insertbackground=color, justify="right")
        entry.pack(fill="x", padx=18, pady=15, ipady=8)
        
        # خط مؤشر
        indicator_line = tk.Frame(field_container, bg=self.colors['gray_medium'], height=3)
        indicator_line.pack(fill="x", pady=(5, 0))
        
        # تأثيرات التفاعل
        self.add_field_effects(entry, placeholder, indicator_line, color)
        
        return {"entry": entry, "indicator": indicator_line}

    def create_compact_input_field(self, parent, icon, label, placeholder, color):
        """إنشاء حقل إدخال مدمج للشبكة"""
        # إطار الحقل
        field_container = tk.Frame(parent, bg=self.colors['white'])
        field_container.pack(fill="both", expand=True)

        # تسمية الحقل
        label_frame = tk.Frame(field_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 8))

        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")

        # النص والأيقونة
        tk.Label(label_content, text=label, font=self.fonts['normal'],
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 6))

        tk.Label(label_content, text=icon, font=("Tahoma", 12),
                bg=self.colors['white'], fg=color).pack(side="right")

        # إطار الحقل
        input_frame = tk.Frame(field_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        input_frame.pack(fill="x")

        # حقل الإدخال
        entry = tk.Entry(input_frame, font=self.fonts['arabic'], relief="flat", bd=0,
                        bg=self.colors['gray_light'], fg=self.colors['text_primary'],
                        insertbackground=color, justify="right")
        entry.pack(fill="x", padx=12, pady=10, ipady=6)

        # خط مؤشر
        indicator_line = tk.Frame(field_container, bg=self.colors['gray_medium'], height=2)
        indicator_line.pack(fill="x", pady=(3, 0))

        # تأثيرات التفاعل
        self.add_field_effects(entry, placeholder, indicator_line, color)

        return {"entry": entry, "indicator": indicator_line}
        
    def create_textarea_field(self, parent, icon, label, placeholder, color):
        """إنشاء منطقة نص"""
        # إطار المنطقة
        textarea_container = tk.Frame(parent, bg=self.colors['white'])
        textarea_container.pack(fill="x", pady=15)
        
        # تسمية المنطقة
        label_frame = tk.Frame(textarea_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        # إطار منطقة النص
        text_frame = tk.Frame(textarea_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        text_frame.pack(fill="both", expand=True)
        
        # منطقة النص
        textarea = tk.Text(text_frame, height=6, font=self.fonts['arabic'],
                          relief="flat", bd=0, bg=self.colors['gray_light'], fg=self.colors['text_primary'],
                          wrap=tk.WORD, insertbackground=color)
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=textarea.yview)
        textarea.configure(yscrollcommand=scrollbar.set)
        
        # ترتيب RTL
        scrollbar.pack(side="left", fill="y")
        textarea.pack(side="right", fill="both", expand=True, padx=18, pady=15)
        
        # إضافة placeholder
        self.add_textarea_placeholder(textarea, placeholder)
        
        return {"textarea": textarea, "scrollbar": scrollbar}
        
    def create_options_field(self, parent, icon, label):
        """إنشاء قسم خيارات"""
        options_container = tk.Frame(parent, bg=self.colors['white'])
        options_container.pack(fill="x", pady=15)
        
        # تسمية الخيارات
        label_frame = tk.Frame(options_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 18))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=self.colors['text_primary']).pack(side="right")
        
        # إطار الخيارات
        options_frame = tk.Frame(options_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        options_frame.pack(fill="x")
        
        return options_frame
        
    def create_radio_option(self, parent, icon, text, value, color):
        """إنشاء خيار راديو"""
        option_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        option_frame.pack(fill="x", pady=6, padx=15)
        
        rb = tk.Radiobutton(option_frame, text=f"{icon} {text}", variable=self.funding_type,
                          value=value, bg=self.colors['gray_light'], font=self.fonts['normal'],
                          fg=color, selectcolor=self.colors['accent'], cursor="hand2", pady=12,
                          anchor="e", justify="right")
        rb.pack(anchor="e", fill="x", padx=25)
        
    def add_field_effects(self, entry, placeholder, indicator, color):
        """إضافة تأثيرات للحقول"""
        # إضافة placeholder
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")
        
        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg=self.colors['text_primary'], bg=self.colors['white'])
            indicator.config(bg=color)
            entry.master.config(relief="solid", bd=2, highlightbackground=color)
        
        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8", bg=self.colors['gray_light'])
            indicator.config(bg=self.colors['gray_medium'])
            entry.master.config(relief="solid", bd=1, highlightbackground=self.colors['gray_medium'])
        
        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)
        
    def add_textarea_placeholder(self, textarea, placeholder):
        """إضافة placeholder لمنطقة النص"""
        textarea.insert("1.0", placeholder)
        textarea.config(fg="#94a3b8")
        textarea.tag_configure("rtl", justify="right")
        textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_in(event):
            if textarea.get("1.0", "end-1c") == placeholder:
                textarea.delete("1.0", tk.END)
                textarea.config(fg=self.colors['text_primary'])
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_out(event):
            if textarea.get("1.0", "end-1c").strip() == "":
                textarea.insert("1.0", placeholder)
                textarea.config(fg="#94a3b8")
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        textarea.bind("<FocusIn>", on_focus_in)
        textarea.bind("<FocusOut>", on_focus_out)
        
    def get_data(self):
        """الحصول على بيانات القسم المحدث"""
        data = {}

        # البيانات الشخصية
        for key, entry in self.personal_entries.items():
            data[key] = entry.get()

        # وصف المشروع
        data['project_name'] = self.project_name_entry.get()
        data['location'] = self.location_entry.get()
        data['idea_importance'] = self.idea_importance_text.get("1.0", "end-1c")
        data['required_skills'] = self.required_skills_text.get("1.0", "end-1c")
        data['community_need'] = self.community_need_text.get("1.0", "end-1c")

        # التمويل والتكاليف
        data['grant_value'] = self.grant_value_entry.get()
        data['self_funding'] = self.self_funding_entry.get()
        data['total_cost'] = self.total_cost_entry.get()

        # تفاصيل المشروع
        data['license_required'] = self.license_required.get()
        data['target_group'] = self.target_group.get()
        data['other_target'] = self.other_target_entry.get()

        return data
        
    def set_data(self, data):
        """تعيين بيانات القسم المحدث"""
        try:
            # تحميل البيانات الشخصية
            for key, entry in self.personal_entries.items():
                if key in data:
                    entry.delete(0, tk.END)
                    entry.insert(0, data[key])

            # تحميل وصف المشروع
            if 'project_name' in data:
                self.project_name_entry.delete(0, tk.END)
                self.project_name_entry.insert(0, data['project_name'])

            if 'location' in data:
                self.location_entry.delete(0, tk.END)
                self.location_entry.insert(0, data['location'])

            if 'idea_importance' in data:
                self.idea_importance_text.delete("1.0", tk.END)
                self.idea_importance_text.insert("1.0", data['idea_importance'])

            if 'required_skills' in data:
                self.required_skills_text.delete("1.0", tk.END)
                self.required_skills_text.insert("1.0", data['required_skills'])

            if 'community_need' in data:
                self.community_need_text.delete("1.0", tk.END)
                self.community_need_text.insert("1.0", data['community_need'])

            # تحميل التمويل والتكاليف
            if 'grant_value' in data:
                self.grant_value_entry.delete(0, tk.END)
                self.grant_value_entry.insert(0, data['grant_value'])

            if 'self_funding' in data:
                self.self_funding_entry.delete(0, tk.END)
                self.self_funding_entry.insert(0, data['self_funding'])

            if 'total_cost' in data:
                self.total_cost_entry.delete(0, tk.END)
                self.total_cost_entry.insert(0, data['total_cost'])

            # تحميل تفاصيل المشروع
            if 'license_required' in data:
                self.license_required.set(data['license_required'])

            if 'target_group' in data:
                self.target_group.set(data['target_group'])

            if 'other_target' in data:
                self.other_target_entry.delete(0, tk.END)
                self.other_target_entry.insert(0, data['other_target'])

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def clear_data(self):
        """مسح بيانات القسم المحدث"""
        try:
            # مسح البيانات الشخصية
            for entry in self.personal_entries.values():
                entry.delete(0, tk.END)

            # مسح وصف المشروع
            self.project_name_entry.delete(0, tk.END)
            self.location_entry.delete(0, tk.END)
            self.idea_importance_text.delete("1.0", tk.END)
            self.required_skills_text.delete("1.0", tk.END)
            self.community_need_text.delete("1.0", tk.END)

            # مسح التمويل والتكاليف
            self.grant_value_entry.delete(0, tk.END)
            self.self_funding_entry.delete(0, tk.END)
            self.total_cost_entry.delete(0, tk.END)

            # مسح تفاصيل المشروع
            self.license_required.set("نعم")
            self.target_group.set("شباب")
            self.other_target_entry.delete(0, tk.END)

        except Exception as e:
            print(f"خطأ في مسح البيانات: {e}")
