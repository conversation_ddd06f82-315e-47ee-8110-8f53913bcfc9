#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة البيانات - حفظ وتحميل المشاريع
Data Management - Save and Load Projects

وظائف الحفظ والتحميل للمشاريع بصيغة JSON
"""

import json
import os
from datetime import datetime
from tkinter import filedialog, messagebox

class DataManager:
    """مدير البيانات للحفظ والتحميل"""
    
    def __init__(self):
        self.data_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
        self.ensure_data_folder()
        
    def ensure_data_folder(self):
        """التأكد من وجود مجلد البيانات"""
        if not os.path.exists(self.data_folder):
            os.makedirs(self.data_folder)
            
    def save_project(self, project_data, filename=None):
        """حفظ المشروع"""
        try:
            if not filename:
                # اختيار مكان الحفظ
                filename = filedialog.asksaveasfilename(
                    title="حفظ المشروع",
                    defaultextension=".json",
                    filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
                    initialdir=self.data_folder
                )
                
            if filename:
                # إضافة معلومات إضافية
                save_data = {
                    'project_info': {
                        'created_date': datetime.now().isoformat(),
                        'version': '2.0',
                        'app_name': 'نظام تخطيط المشاريع الشامل'
                    },
                    'project_data': project_data
                }
                
                # حفظ الملف
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, ensure_ascii=False, indent=2)
                    
                return True
                
        except Exception as e:
            raise Exception(f"خطأ في حفظ المشروع: {str(e)}")
            
        return False
        
    def load_project(self, filename=None):
        """تحميل المشروع"""
        try:
            if not filename:
                # اختيار ملف للتحميل
                filename = filedialog.askopenfilename(
                    title="تحميل المشروع",
                    filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
                    initialdir=self.data_folder
                )
                
            if filename and os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # التحقق من صحة البيانات
                if 'project_data' in data:
                    return data['project_data']
                else:
                    # ملف قديم بدون هيكل جديد
                    return data
                    
        except Exception as e:
            raise Exception(f"خطأ في تحميل المشروع: {str(e)}")
            
        return None
        
    def auto_save(self, project_data):
        """حفظ تلقائي"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.data_folder, f"auto_save_{timestamp}.json")
            
            save_data = {
                'project_info': {
                    'created_date': datetime.now().isoformat(),
                    'version': '2.0',
                    'type': 'auto_save',
                    'app_name': 'نظام تخطيط المشاريع الشامل'
                },
                'project_data': project_data
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
                
            # حذف الملفات القديمة (الاحتفاظ بآخر 5 ملفات)
            self.cleanup_auto_saves()
            
            return filename
            
        except Exception as e:
            print(f"خطأ في الحفظ التلقائي: {str(e)}")
            return None
            
    def cleanup_auto_saves(self, keep_count=5):
        """تنظيف ملفات الحفظ التلقائي القديمة"""
        try:
            auto_save_files = []
            for filename in os.listdir(self.data_folder):
                if filename.startswith('auto_save_') and filename.endswith('.json'):
                    filepath = os.path.join(self.data_folder, filename)
                    auto_save_files.append((filepath, os.path.getmtime(filepath)))
                    
            # ترتيب حسب التاريخ (الأحدث أولاً)
            auto_save_files.sort(key=lambda x: x[1], reverse=True)
            
            # حذف الملفات الزائدة
            for filepath, _ in auto_save_files[keep_count:]:
                os.remove(filepath)
                
        except Exception as e:
            print(f"خطأ في تنظيف ملفات الحفظ التلقائي: {str(e)}")
            
    def export_to_csv(self, project_data, filename=None):
        """تصدير البيانات إلى CSV"""
        try:
            import csv
            
            if not filename:
                filename = filedialog.asksaveasfilename(
                    title="تصدير إلى CSV",
                    defaultextension=".csv",
                    filetypes=[("ملفات CSV", "*.csv"), ("جميع الملفات", "*.*")],
                    initialdir=self.data_folder
                )
                
            if filename:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    
                    # كتابة رأس الملف
                    writer.writerow(['القسم', 'الحقل', 'القيمة'])
                    
                    # كتابة البيانات
                    for section, data in project_data.items():
                        if isinstance(data, dict):
                            for field, value in data.items():
                                writer.writerow([section, field, str(value)])
                        else:
                            writer.writerow([section, 'البيانات', str(data)])
                            
                return True
                
        except Exception as e:
            raise Exception(f"خطأ في تصدير CSV: {str(e)}")
            
        return False
        
    def get_recent_projects(self, count=5):
        """الحصول على المشاريع الحديثة"""
        try:
            projects = []
            
            for filename in os.listdir(self.data_folder):
                if filename.endswith('.json') and not filename.startswith('auto_save_'):
                    filepath = os.path.join(self.data_folder, filename)
                    modified_time = os.path.getmtime(filepath)
                    projects.append({
                        'filename': filename,
                        'filepath': filepath,
                        'modified_time': modified_time,
                        'modified_date': datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M")
                    })
                    
            # ترتيب حسب التاريخ (الأحدث أولاً)
            projects.sort(key=lambda x: x['modified_time'], reverse=True)
            
            return projects[:count]
            
        except Exception as e:
            print(f"خطأ في الحصول على المشاريع الحديثة: {str(e)}")
            return []
            
    def create_backup(self, project_data):
        """إنشاء نسخة احتياطية"""
        try:
            backup_folder = os.path.join(self.data_folder, 'backups')
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
                
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(backup_folder, f"backup_{timestamp}.json")
            
            backup_data = {
                'backup_info': {
                    'created_date': datetime.now().isoformat(),
                    'version': '2.0',
                    'type': 'backup',
                    'app_name': 'نظام تخطيط المشاريع الشامل'
                },
                'project_data': project_data
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2)
                
            return filename
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            return None
            
    def validate_project_data(self, data):
        """التحقق من صحة بيانات المشروع"""
        try:
            required_sections = [
                'personal_info', 'market_analysis', 'swot_analysis',
                'marketing_mix', 'requirements', 'startup_costs',
                'operating_costs', 'financial_summary', 
                'annual_revenue', 'profit_loss'
            ]
            
            if not isinstance(data, dict):
                return False, "البيانات يجب أن تكون في شكل قاموس"
                
            missing_sections = []
            for section in required_sections:
                if section not in data:
                    missing_sections.append(section)
                    
            if missing_sections:
                return False, f"أقسام مفقودة: {', '.join(missing_sections)}"
                
            return True, "البيانات صحيحة"
            
        except Exception as e:
            return False, f"خطأ في التحقق من البيانات: {str(e)}"
