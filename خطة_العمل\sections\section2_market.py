#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
القسم الثاني: دراسة السوق والمنافسين
Section 2: Market Analysis and Competitors

تحليل شامل للسوق المستهدف والمنافسين والفرص التجارية
"""

import tkinter as tk
from tkinter import ttk

class MarketAnalysisSection:
    """قسم دراسة السوق"""
    
    def __init__(self, parent, colors, fonts):
        self.parent = parent
        self.colors = colors
        self.fonts = fonts
        self.data = {}
        
        self.create_section()
        
    def create_section(self):
        """إنشاء محتوى القسم"""
        # === الحاوي الرئيسي ===
        main_container = tk.Frame(self.parent, bg=self.colors['light'])
        main_container.pack(fill="both", expand=True, padx=30, pady=30)
        
        # === رأس القسم ===
        self.create_header(main_container)
        
        # === منطقة المحتوى ===
        content_area = tk.Frame(main_container, bg=self.colors['white'], relief="solid", bd=1)
        content_area.pack(fill="both", expand=True, pady=(0, 30))
        
        # === المحتوى الداخلي ===
        inner_content = tk.Frame(content_area, bg=self.colors['white'])
        inner_content.pack(fill="both", expand=True, padx=40, pady=35)
        
        # === قسم المنتجات والخدمات ===
        self.create_products_services_section(inner_content)
        
        # === قسم السوق المستهدف ===
        self.create_target_market_section(inner_content)
        
        # === قسم تحليل المنافسين ===
        self.create_competitors_section(inner_content)
        
        # === قسم الفرص والتهديدات ===
        self.create_opportunities_threats_section(inner_content)
        
    def create_header(self, parent):
        """إنشاء رأس القسم"""
        header_container = tk.Frame(parent, bg=self.colors['light'])
        header_container.pack(fill="x", pady=(0, 35))
        
        header_frame = tk.Frame(header_container, bg=self.colors['primary'], relief="solid", bd=2)
        header_frame.pack(fill="x")
        
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(fill="x", padx=45, pady=30)
        
        title_label = tk.Label(header_content, 
                              text="📊 دراسة السوق والتحليل التنافسي",
                              font=self.fonts['title'], 
                              bg=self.colors['primary'], 
                              fg=self.colors['white'],
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        subtitle_label = tk.Label(header_content,
                                 text="تحليل شامل للسوق المستهدف والمنافسين والفرص التجارية",
                                 font=self.fonts['normal'],
                                 bg=self.colors['primary'],
                                 fg=self.colors['gray_medium'],
                                 anchor="e", justify="right")
        subtitle_label.pack(anchor="e", fill="x", pady=(10, 0))
        
        gold_line = tk.Frame(header_container, bg=self.colors['accent'], height=4)
        gold_line.pack(fill="x")
        
    def create_products_services_section(self, parent):
        """قسم المنتجات والخدمات"""
        section = self.create_section_frame(parent, "🛍️ المنتجات والخدمات المقدمة", self.colors['info'])
        
        # شبكة المنتجات والخدمات
        grid_container = tk.Frame(section, bg=self.colors['white'])
        grid_container.pack(fill="both", expand=True, pady=15)
        
        # المنتجات (الجانب الأيمن)
        products_frame = self.create_card_frame(grid_container, "🛍️ المنتجات", self.colors['info'], "right")
        products_field = self.create_textarea_in_card(products_frame, "📦", "قائمة المنتجات التفصيلية", 
                                                     "• منتج رقم 1: الوصف والمواصفات\n• منتج رقم 2: الوصف والمواصفات\n• منتج رقم 3: الوصف والمواصفات", 
                                                     self.colors['info'])
        self.products_entry = products_field["textarea"]
        
        # الخدمات (الجانب الأيسر)
        services_frame = self.create_card_frame(grid_container, "🔧 الخدمات", self.colors['warning'], "left")
        services_field = self.create_textarea_in_card(services_frame, "⚙️", "قائمة الخدمات التفصيلية", 
                                                     "• خدمة رقم 1: الوصف والتفاصيل\n• خدمة رقم 2: الوصف والتفاصيل\n• خدمة رقم 3: الوصف والتفاصيل", 
                                                     self.colors['warning'])
        self.services_entry = services_field["textarea"]
        
    def create_target_market_section(self, parent):
        """قسم السوق المستهدف"""
        section = self.create_section_frame(parent, "🎯 تحليل السوق المستهدف", self.colors['success'])
        
        # الفئة المستهدفة
        target_field = self.create_textarea_field(section, "👥", "الفئة المستهدفة والعملاء المحتملين", 
                                                 "• الفئة العمرية: (مثال: 25-45 سنة)\n• المستوى الاقتصادي: (مثال: متوسط إلى عالي)\n• الاهتمامات: (مثال: التكنولوجيا، الصحة)\n• الموقع الجغرافي: (مثال: المدن الكبرى)", 
                                                 self.colors['success'])
        self.target_market_entry = target_field["textarea"]
        
        # حجم السوق
        market_size_field = self.create_input_field(section, "📈", "حجم السوق المتوقع (بالريال السعودي)", 
                                                   "مثال: 50,000,000 ريال سنوياً", self.colors['success'])
        self.market_size_entry = market_size_field["entry"]
        
        # نمو السوق
        growth_field = self.create_input_field(section, "📊", "معدل نمو السوق السنوي المتوقع (%)", 
                                              "مثال: 15% سنوياً", self.colors['success'])
        self.market_growth_entry = growth_field["entry"]
        
    def create_competitors_section(self, parent):
        """قسم تحليل المنافسين"""
        section = self.create_section_frame(parent, "⚔️ تحليل المنافسين التفصيلي", self.colors['danger'])
        
        # وجود المنافسين
        competitors_existence = self.create_options_field(section, "❓", "هل يوجد منافسون مباشرون في السوق؟")
        
        self.has_competitors = tk.StringVar(value="نعم")
        
        options_data = [
            ("✅", "نعم، يوجد منافسون مباشرون", "نعم", self.colors['success']),
            ("❌", "لا، لا يوجد منافسون مباشرون", "لا", self.colors['danger']),
            ("⚠️", "يوجد منافسون غير مباشرين فقط", "غير مباشر", self.colors['warning'])
        ]
        
        for icon, text, value, color in options_data:
            self.create_radio_option(competitors_existence, icon, text, value, color)
        
        # شبكة تفاصيل المنافسين
        competitors_grid = tk.Frame(section, bg=self.colors['white'])
        competitors_grid.pack(fill="both", expand=True, pady=20)
        
        # عدد المنافسين (الجانب الأيمن)
        count_frame = self.create_card_frame(competitors_grid, "🔢 عدد المنافسين", self.colors['danger'], "right")
        count_field = self.create_input_in_card(count_frame, "📊", "العدد الإجمالي للمنافسين", 
                                               "مثال: 5 منافسين رئيسيين", self.colors['danger'])
        self.competitor_count = count_field["entry"]
        
        # تحليل المنافسين (الجانب الأيسر)
        analysis_frame = self.create_card_frame(competitors_grid, "🔍 تحليل المنافسين", self.colors['warning'], "left")
        analysis_field = self.create_textarea_in_card(analysis_frame, "🔍", "تحليل تفصيلي للمنافسين", 
                                                     "• المنافس الأول: الاسم، نقاط القوة، نقاط الضعف\n• المنافس الثاني: الاسم، نقاط القوة، نقاط الضعف\n• المنافس الثالث: الاسم، نقاط القوة، نقاط الضعف", 
                                                     self.colors['warning'])
        self.comp_products_entry = analysis_field["textarea"]
        
    def create_opportunities_threats_section(self, parent):
        """قسم الفرص والتهديدات"""
        section = self.create_section_frame(parent, "⚡ الفرص والتهديدات السوقية", self.colors['warning'])
        
        # شبكة الفرص والتهديدات
        ot_grid = tk.Frame(section, bg=self.colors['white'])
        ot_grid.pack(fill="both", expand=True, pady=15)
        
        # الفرص (الجانب الأيمن)
        opportunities_frame = self.create_card_frame(ot_grid, "🌟 الفرص المتاحة", self.colors['success'], "right")
        opportunities_field = self.create_textarea_in_card(opportunities_frame, "💡", "الفرص السوقية المتاحة", 
                                                          "• فرصة رقم 1: وصف الفرصة وكيفية استغلالها\n• فرصة رقم 2: وصف الفرصة وكيفية استغلالها\n• فرصة رقم 3: وصف الفرصة وكيفية استغلالها", 
                                                          self.colors['success'])
        self.market_opportunities_entry = opportunities_field["textarea"]
        
        # التهديدات (الجانب الأيسر)
        threats_frame = self.create_card_frame(ot_grid, "⚠️ التهديدات المحتملة", self.colors['danger'], "left")
        threats_field = self.create_textarea_in_card(threats_frame, "⚡", "التهديدات السوقية المحتملة", 
                                                    "• تهديد رقم 1: وصف التهديد وكيفية التعامل معه\n• تهديد رقم 2: وصف التهديد وكيفية التعامل معه\n• تهديد رقم 3: وصف التهديد وكيفية التعامل معه", 
                                                    self.colors['danger'])
        self.market_threats_entry = threats_field["textarea"]
        
    def create_section_frame(self, parent, title, color):
        """إنشاء إطار قسم"""
        section_title_frame = tk.Frame(parent, bg=self.colors['white'])
        section_title_frame.pack(fill="x", pady=(30, 25))
        
        title_label = tk.Label(section_title_frame, text=title, 
                              font=self.fonts['title'], bg=self.colors['white'], fg=color,
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")
        
        title_line = tk.Frame(section_title_frame, bg=color, height=3)
        title_line.pack(fill="x", pady=(8, 0))
        
        section_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        section_frame.pack(fill="x", pady=(0, 25))
        
        section_content = tk.Frame(section_frame, bg=self.colors['white'])
        section_content.pack(fill="x", padx=30, pady=25)
        
        return section_content
        
    def create_card_frame(self, parent, title, color, position):
        """إنشاء إطار بطاقة"""
        if position == "right":
            side = "right"
            padx = (10, 0)
        else:  # left
            side = "left"
            padx = (0, 10)
            
        card_container = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        card_container.pack(side=side, fill="both", expand=True, padx=padx)
        
        card_header = tk.Frame(card_container, bg=color, height=50)
        card_header.pack(fill="x")
        card_header.pack_propagate(False)
        
        tk.Label(card_header, text=title, font=self.fonts['subtitle'], 
                bg=color, fg="white", anchor="e").pack(expand=True, padx=20)
        
        card_content = tk.Frame(card_container, bg=self.colors['white'])
        card_content.pack(fill="both", expand=True, padx=20, pady=20)
        
        return card_content
        
    def create_input_field(self, parent, icon, label, placeholder, color):
        """إنشاء حقل إدخال"""
        field_container = tk.Frame(parent, bg=self.colors['white'])
        field_container.pack(fill="x", pady=15)
        
        label_frame = tk.Frame(field_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        input_frame = tk.Frame(field_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        input_frame.pack(fill="x")
        
        entry = tk.Entry(input_frame, font=self.fonts['arabic'], relief="flat", bd=0,
                        bg=self.colors['gray_light'], fg=self.colors['text_primary'], 
                        insertbackground=color, justify="right")
        entry.pack(fill="x", padx=18, pady=15, ipady=8)
        
        indicator_line = tk.Frame(field_container, bg=self.colors['gray_medium'], height=3)
        indicator_line.pack(fill="x", pady=(5, 0))
        
        self.add_field_effects(entry, placeholder, indicator_line, color)
        
        return {"entry": entry, "indicator": indicator_line}
        
    def create_textarea_field(self, parent, icon, label, placeholder, color):
        """إنشاء منطقة نص"""
        textarea_container = tk.Frame(parent, bg=self.colors['white'])
        textarea_container.pack(fill="x", pady=15)
        
        label_frame = tk.Frame(textarea_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 10))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=color).pack(side="right")
        
        text_frame = tk.Frame(textarea_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        text_frame.pack(fill="both", expand=True)
        
        textarea = tk.Text(text_frame, height=6, font=self.fonts['arabic'],
                          relief="flat", bd=0, bg=self.colors['gray_light'], fg=self.colors['text_primary'],
                          wrap=tk.WORD, insertbackground=color)
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=textarea.yview)
        textarea.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side="left", fill="y")
        textarea.pack(side="right", fill="both", expand=True, padx=18, pady=15)
        
        self.add_textarea_placeholder(textarea, placeholder)
        
        return {"textarea": textarea, "scrollbar": scrollbar}
        
    def create_input_in_card(self, parent, icon, label, placeholder, color):
        """إنشاء حقل إدخال داخل بطاقة"""
        return self.create_input_field(parent, icon, label, placeholder, color)
        
    def create_textarea_in_card(self, parent, icon, label, placeholder, color):
        """إنشاء منطقة نص داخل بطاقة"""
        return self.create_textarea_field(parent, icon, label, placeholder, color)
        
    def create_options_field(self, parent, icon, label):
        """إنشاء قسم خيارات"""
        options_container = tk.Frame(parent, bg=self.colors['white'])
        options_container.pack(fill="x", pady=15)
        
        label_frame = tk.Frame(options_container, bg=self.colors['white'])
        label_frame.pack(fill="x", pady=(0, 18))
        
        label_content = tk.Frame(label_frame, bg=self.colors['white'])
        label_content.pack(anchor="e")
        
        tk.Label(label_content, text=label, font=self.fonts['normal'], 
                bg=self.colors['white'], fg=self.colors['text_primary'],
                anchor="e", justify="right").pack(side="right", padx=(0, 8))
        
        tk.Label(label_content, text=icon, font=("Tahoma", 14), 
                bg=self.colors['white'], fg=self.colors['text_primary']).pack(side="right")
        
        options_frame = tk.Frame(options_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        options_frame.pack(fill="x")
        
        return options_frame
        
    def create_radio_option(self, parent, icon, text, value, color):
        """إنشاء خيار راديو"""
        option_frame = tk.Frame(parent, bg=self.colors['gray_light'], relief="solid", bd=1)
        option_frame.pack(fill="x", pady=6, padx=15)
        
        rb = tk.Radiobutton(option_frame, text=f"{icon} {text}", variable=self.has_competitors,
                          value=value, bg=self.colors['gray_light'], font=self.fonts['normal'],
                          fg=color, selectcolor=self.colors['accent'], cursor="hand2", pady=12,
                          anchor="e", justify="right")
        rb.pack(anchor="e", fill="x", padx=25)
        
    def add_field_effects(self, entry, placeholder, indicator, color):
        """إضافة تأثيرات للحقول"""
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")
        
        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg=self.colors['text_primary'], bg=self.colors['white'])
            indicator.config(bg=color)
            entry.master.config(relief="solid", bd=2, highlightbackground=color)
        
        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8", bg=self.colors['gray_light'])
            indicator.config(bg=self.colors['gray_medium'])
            entry.master.config(relief="solid", bd=1, highlightbackground=self.colors['gray_medium'])
        
        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)
        
    def add_textarea_placeholder(self, textarea, placeholder):
        """إضافة placeholder لمنطقة النص"""
        textarea.insert("1.0", placeholder)
        textarea.config(fg="#94a3b8")
        textarea.tag_configure("rtl", justify="right")
        textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_in(event):
            if textarea.get("1.0", "end-1c") == placeholder:
                textarea.delete("1.0", tk.END)
                textarea.config(fg=self.colors['text_primary'])
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        def on_focus_out(event):
            if textarea.get("1.0", "end-1c").strip() == "":
                textarea.insert("1.0", placeholder)
                textarea.config(fg="#94a3b8")
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")
        
        textarea.bind("<FocusIn>", on_focus_in)
        textarea.bind("<FocusOut>", on_focus_out)
        
    def get_data(self):
        """الحصول على بيانات القسم"""
        data = {
            'products': self.products_entry.get("1.0", "end-1c"),
            'services': self.services_entry.get("1.0", "end-1c"),
            'target_market': self.target_market_entry.get("1.0", "end-1c"),
            'market_size': self.market_size_entry.get(),
            'market_growth': self.market_growth_entry.get(),
            'has_competitors': self.has_competitors.get(),
            'competitor_count': self.competitor_count.get(),
            'competitor_analysis': self.comp_products_entry.get("1.0", "end-1c"),
            'market_opportunities': self.market_opportunities_entry.get("1.0", "end-1c"),
            'market_threats': self.market_threats_entry.get("1.0", "end-1c")
        }
        return data
        
    def set_data(self, data):
        """تعيين بيانات القسم"""
        # تنفيذ تحميل البيانات
        pass
        
    def clear_data(self):
        """مسح بيانات القسم"""
        # تنفيذ مسح البيانات
        pass
