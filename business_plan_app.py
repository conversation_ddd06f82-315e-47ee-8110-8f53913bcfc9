#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق خطة العمل الشاملة
Business Plan Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class BusinessPlanApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تطبيق خطة العمل الشاملة")
        self.root.geometry("1200x900")
        self.root.configure(bg="#f5f7fa")
        
        # تخزين البيانات
        self.data = {}
        
        # إعداد الخطوط والألوان
        self.label_font = ("Arial", 11, "bold")
        self.entry_font = ("Arial", 10)
        self.box_bg = "#ffffff"
        self.primary_color = "#2c3e50"
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        # شريط القوائم
        self.create_menu()
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        title_frame.pack(fill="x", padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📋 تطبيق خطة العمل الشاملة", 
                              font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(expand=True)
        
        # دفتر التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء التبويبات
        self.create_tabs()
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_project)
        file_menu.add_command(label="فتح", command=self.open_project)
        file_menu.add_command(label="حفظ", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير PDF", command=self.export_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_tabs(self):
        """إنشاء جميع التبويبات"""
        # التبويب 1: المعلومات الشخصية
        self.tab1 = self.create_personal_info_tab()
        
        # التبويب 2: دراسة السوق والمنافسين
        self.tab2 = self.create_market_analysis_tab()
        
        # التبويب 3: تحليل SWOT
        self.tab3 = self.create_swot_analysis_tab()
        
        # التبويب 4: المزيج التسويقي
        self.tab4 = self.create_marketing_mix_tab()
        
        # التبويب 5: مستلزمات الإنتاج
        self.tab5 = self.create_production_requirements_tab()
        
        # التبويب 6: الدراسة المالية - التأسيسية
        self.tab6 = self.create_startup_financial_tab()
        
        # التبويب 7: الدراسة المالية - التشغيلية
        self.tab7 = self.create_operational_financial_tab()
        
        # التبويب 8: الدراسة المالية - ملخص شامل
        self.tab8 = self.create_financial_summary_tab()
        
        # التبويب 9: الإيرادات السنوية
        self.tab9 = self.create_annual_revenue_tab()
        
        # التبويب 10: الربح والخسارة السنوي
        self.tab10 = self.create_annual_profit_loss_tab()
    
    def create_personal_info_tab(self):
        """إنشاء تبويب المعلومات الشخصية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="1️⃣ المعلومات الشخصية")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المعلومات الشخصية
        personal_frame = tk.LabelFrame(scrollable_frame, text="المعلومات الشخصية", 
                                     font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        personal_frame.pack(fill="x", padx=10, pady=10)
        
        # الحقول
        fields = [
            ("الاسم الكامل:", "full_name"),
            ("العمر:", "age"),
            ("المؤهل العلمي:", "education"),
            ("الخبرة السابقة:", "experience"),
            ("رقم الهاتف:", "phone"),
            ("البريد الإلكتروني:", "email")
        ]
        
        self.personal_entries = {}
        for i, (label, key) in enumerate(fields):
            tk.Label(personal_frame, text=label, font=self.label_font, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(personal_frame, width=50, font=self.entry_font)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            self.personal_entries[key] = entry
        
        # معلومات المشروع
        project_frame = tk.LabelFrame(scrollable_frame, text="معلومات المشروع", 
                                    font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        project_frame.pack(fill="x", padx=10, pady=10)
        
        # اسم المشروع
        tk.Label(project_frame, text="اسم المشروع:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.project_name_entry = tk.Entry(project_frame, width=50, font=self.entry_font)
        self.project_name_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        # وصف المشروع
        tk.Label(project_frame, text="وصف المشروع:", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="ne", padx=10, pady=5)
        self.project_desc_text = tk.Text(project_frame, width=60, height=4, font=self.entry_font)
        self.project_desc_text.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        # نوع التمويل
        funding_frame = tk.LabelFrame(scrollable_frame, text="التمويل", 
                                    font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        funding_frame.pack(fill="x", padx=10, pady=10)
        
        tk.Label(funding_frame, text="نوع التمويل:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        
        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = ["ذاتي", "قرض بنكي", "مستثمر", "مختلط"]
        for i, option in enumerate(funding_options):
            tk.Radiobutton(funding_frame, text=option, variable=self.funding_type, 
                          value=option, bg=self.box_bg).grid(row=0, column=i+1, padx=5)
        
        # مبلغ التمويل
        tk.Label(funding_frame, text="مبلغ التمويل المطلوب:", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.funding_amount_entry = tk.Entry(funding_frame, width=20, font=self.entry_font)
        self.funding_amount_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return tab
    
    def new_project(self):
        """مشروع جديد"""
        result = messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.")
        if result:
            self.clear_all_data()
    
    def clear_all_data(self):
        """مسح جميع البيانات"""
        # مسح المعلومات الشخصية
        for entry in self.personal_entries.values():
            entry.delete(0, tk.END)
        
        self.project_name_entry.delete(0, tk.END)
        self.project_desc_text.delete(1.0, tk.END)
        self.funding_amount_entry.delete(0, tk.END)
        self.funding_type.set("ذاتي")
    
    def open_project(self):
        """فتح مشروع"""
        filename = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                self.load_data_to_ui()
                messagebox.showinfo("نجح", "تم فتح المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")
    
    def save_project(self):
        """حفظ المشروع"""
        filename = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.collect_data_from_ui()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")
    
    def collect_data_from_ui(self):
        """جمع البيانات من واجهة المستخدم"""
        # المعلومات الشخصية
        personal_data = {}
        for key, entry in self.personal_entries.items():
            personal_data[key] = entry.get()
        
        self.data['personal_info'] = personal_data
        self.data['project_name'] = self.project_name_entry.get()
        self.data['project_description'] = self.project_desc_text.get(1.0, tk.END).strip()
        self.data['funding_type'] = self.funding_type.get()
        self.data['funding_amount'] = self.funding_amount_entry.get()
        self.data['last_modified'] = datetime.now().isoformat()
    
    def load_data_to_ui(self):
        """تحميل البيانات إلى واجهة المستخدم"""
        if 'personal_info' in self.data:
            for key, entry in self.personal_entries.items():
                if key in self.data['personal_info']:
                    entry.delete(0, tk.END)
                    entry.insert(0, self.data['personal_info'][key])
        
        if 'project_name' in self.data:
            self.project_name_entry.delete(0, tk.END)
            self.project_name_entry.insert(0, self.data['project_name'])
        
        if 'project_description' in self.data:
            self.project_desc_text.delete(1.0, tk.END)
            self.project_desc_text.insert(1.0, self.data['project_description'])
        
        if 'funding_type' in self.data:
            self.funding_type.set(self.data['funding_type'])
        
        if 'funding_amount' in self.data:
            self.funding_amount_entry.delete(0, tk.END)
            self.funding_amount_entry.insert(0, self.data['funding_amount'])
    
    def export_pdf(self):
        """تصدير إلى PDF"""
        messagebox.showinfo("قريباً", "ميزة تصدير PDF ستكون متاحة قريباً")
    
    def create_market_analysis_tab(self):
        """إنشاء تبويب دراسة السوق والمنافسين"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="2️⃣ دراسة السوق والمنافسين")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # القسم 1: المنتجات والخدمات
        products_frame = tk.LabelFrame(scrollable_frame, text="المنتجات والخدمات",
                                     font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        products_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(products_frame, text="المنتجات:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.products_entry = tk.Entry(products_frame, width=80, font=self.entry_font)
        self.products_entry.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(products_frame, text="الخدمات:", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.services_entry = tk.Entry(products_frame, width=80, font=self.entry_font)
        self.services_entry.grid(row=1, column=1, padx=10, pady=5)

        # القسم 2: المنافسون
        competitors_frame = tk.LabelFrame(scrollable_frame, text="تحليل المنافسين",
                                        font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        competitors_frame.pack(fill="x", padx=10, pady=10)

        self.has_competitors = tk.StringVar(value="نعم")
        tk.Label(competitors_frame, text="هل يوجد منافسون؟", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        tk.Radiobutton(competitors_frame, text="نعم", variable=self.has_competitors,
                      value="نعم", bg=self.box_bg).grid(row=0, column=1, sticky="w")
        tk.Radiobutton(competitors_frame, text="لا", variable=self.has_competitors,
                      value="لا", bg=self.box_bg).grid(row=0, column=1, padx=60, sticky="w")

        tk.Label(competitors_frame, text="كم عددهم؟", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.competitor_count = tk.Entry(competitors_frame, width=10, font=self.entry_font)
        self.competitor_count.grid(row=1, column=1, sticky="w", padx=10)

        tk.Label(competitors_frame, text="ما هي المنتجات المنافسة؟", font=self.label_font, bg=self.box_bg).grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        self.comp_products_entry = tk.Entry(competitors_frame, width=80, font=self.entry_font)
        self.comp_products_entry.grid(row=2, column=1, padx=10, pady=5)

        # القسم 3: طرق ربح المنافسين
        profit_frame = tk.LabelFrame(scrollable_frame, text="طرق ربح المنافسين",
                                   font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        profit_frame.pack(fill="x", padx=10, pady=10)

        profit_methods = [
            "السعر المنخفض", "جودة المنتج", "الخدمة المميزة", "التكلفة المنخفضة", "أخرى"
        ]
        self.profit_vars = {}
        for i, method in enumerate(profit_methods):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(profit_frame, text=method, variable=var, bg=self.box_bg)
            cb.grid(row=i//3, column=i%3, padx=10, pady=5, sticky="w")
            self.profit_vars[method] = var

        # القسم 4: مقارنة الأسعار
        price_frame = tk.LabelFrame(scrollable_frame, text="مقارنة الأسعار",
                                  font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=10, pady=10)

        self.price_comp = tk.StringVar(value="نفس السعر")
        tk.Label(price_frame, text="أسعار المنافسين مقارنة بك:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        tk.Radiobutton(price_frame, text="نفس السعر", variable=self.price_comp,
                      value="نفس السعر", bg=self.box_bg).grid(row=0, column=1, sticky="w")
        tk.Radiobutton(price_frame, text="أعلى من سعري", variable=self.price_comp,
                      value="أعلى", bg=self.box_bg).grid(row=0, column=2)
        tk.Radiobutton(price_frame, text="أقل من سعري", variable=self.price_comp,
                      value="أقل", bg=self.box_bg).grid(row=0, column=3)

        # القسم 5: طرق الترويج لدى المنافسين
        promo_frame = tk.LabelFrame(scrollable_frame, text="طرق الترويج لدى المنافسين",
                                  font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        promo_frame.pack(fill="x", padx=10, pady=10)

        promo_methods = [
            "الإنترنت والتواصل الاجتماعي", "البيع المباشر", "زيارة الزبائن", "الإعلان", "أخرى"
        ]
        self.promo_vars = {}
        for i, method in enumerate(promo_methods):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(promo_frame, text=method, variable=var, bg=self.box_bg)
            cb.grid(row=i//3, column=i%3, padx=10, pady=5, sticky="w")
            self.promo_vars[method] = var

        # القسم 6: عدد زبائن المنافسين
        customers_frame = tk.LabelFrame(scrollable_frame, text="مراقبة عدد زبائن المنافسين",
                                      font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        customers_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(customers_frame, text="عدد الزبائن المتوقع يومياً لدى المنافسين:",
                font=self.label_font, bg=self.box_bg).grid(row=0, column=0, padx=10, pady=5)
        self.daily_customers = tk.Entry(customers_frame, width=10, font=self.entry_font)
        self.daily_customers.grid(row=0, column=1, padx=10, pady=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_swot_analysis_tab(self):
        """إنشاء تبويب تحليل SWOT"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="3️⃣ تحليل SWOT")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إطار رئيسي للتحليل
        main_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # نقاط القوة (Strengths)
        strengths_frame = tk.LabelFrame(main_frame, text="💪 نقاط القوة (Strengths)",
                                      font=self.label_font, bg="#e8f5e8", relief="groove", bd=2)
        strengths_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        self.strengths_text = tk.Text(strengths_frame, width=40, height=8, font=self.entry_font)
        self.strengths_text.pack(padx=10, pady=10, fill="both", expand=True)

        # نقاط الضعف (Weaknesses)
        weaknesses_frame = tk.LabelFrame(main_frame, text="⚠️ نقاط الضعف (Weaknesses)",
                                       font=self.label_font, bg="#ffe8e8", relief="groove", bd=2)
        weaknesses_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        self.weaknesses_text = tk.Text(weaknesses_frame, width=40, height=8, font=self.entry_font)
        self.weaknesses_text.pack(padx=10, pady=10, fill="both", expand=True)

        # الفرص (Opportunities)
        opportunities_frame = tk.LabelFrame(main_frame, text="🌟 الفرص (Opportunities)",
                                          font=self.label_font, bg="#e8f0ff", relief="groove", bd=2)
        opportunities_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

        self.opportunities_text = tk.Text(opportunities_frame, width=40, height=8, font=self.entry_font)
        self.opportunities_text.pack(padx=10, pady=10, fill="both", expand=True)

        # التهديدات (Threats)
        threats_frame = tk.LabelFrame(main_frame, text="⚡ التهديدات (Threats)",
                                    font=self.label_font, bg="#fff0e8", relief="groove", bd=2)
        threats_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

        self.threats_text = tk.Text(threats_frame, width=40, height=8, font=self.entry_font)
        self.threats_text.pack(padx=10, pady=10, fill="both", expand=True)

        # تكوين الشبكة للتوسع
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)

        # إرشادات
        instructions_frame = tk.LabelFrame(scrollable_frame, text="إرشادات التحليل",
                                         font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        instructions_frame.pack(fill="x", padx=10, pady=10)

        instructions_text = """
        نقاط القوة: العوامل الداخلية الإيجابية التي تميز مشروعك (مهارات، موارد، خبرات)
        نقاط الضعف: العوامل الداخلية السلبية التي تحتاج لتحسين (نقص خبرة، موارد محدودة)
        الفرص: العوامل الخارجية الإيجابية التي يمكن استغلالها (نمو السوق، تغيير القوانين)
        التهديدات: العوامل الخارجية السلبية التي قد تؤثر سلباً (منافسة، تغيير اقتصادي)
        """

        tk.Label(instructions_frame, text=instructions_text, font=("Arial", 9),
                bg=self.box_bg, justify="right").pack(padx=10, pady=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_marketing_mix_tab(self):
        """إنشاء تبويب المزيج التسويقي (4P)"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="4️⃣ المزيج التسويقي")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المنتج (Product)
        product_frame = tk.LabelFrame(scrollable_frame, text="🛍️ المنتج (Product)",
                                    font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        product_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(product_frame, text="وصف المنتج/الخدمة:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="ne", padx=10, pady=5)
        self.product_desc_text = tk.Text(product_frame, width=60, height=3, font=self.entry_font)
        self.product_desc_text.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(product_frame, text="المزايا التنافسية:", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="ne", padx=10, pady=5)
        self.competitive_advantages_text = tk.Text(product_frame, width=60, height=3, font=self.entry_font)
        self.competitive_advantages_text.grid(row=1, column=1, padx=10, pady=5)

        # السعر (Price)
        price_frame = tk.LabelFrame(scrollable_frame, text="💰 السعر (Price)",
                                  font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(price_frame, text="استراتيجية التسعير:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)

        self.pricing_strategy = tk.StringVar(value="تنافسي")
        pricing_options = ["تنافسي", "اختراق السوق", "كشط السوق", "قيمة مضافة"]
        for i, option in enumerate(pricing_options):
            tk.Radiobutton(price_frame, text=option, variable=self.pricing_strategy,
                          value=option, bg=self.box_bg).grid(row=0, column=i+1, padx=5)

        tk.Label(price_frame, text="السعر المقترح:", font=self.label_font, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.suggested_price_entry = tk.Entry(price_frame, width=20, font=self.entry_font)
        self.suggested_price_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # المكان (Place)
        place_frame = tk.LabelFrame(scrollable_frame, text="📍 المكان/التوزيع (Place)",
                                  font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        place_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(place_frame, text="قنوات التوزيع:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="ne", padx=10, pady=5)

        distribution_channels = [
            "متجر فعلي", "متجر إلكتروني", "وسطاء", "بيع مباشر", "تطبيقات التوصيل"
        ]
        self.distribution_vars = {}
        for i, channel in enumerate(distribution_channels):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(place_frame, text=channel, variable=var, bg=self.box_bg)
            cb.grid(row=i//3, column=i%3+1, padx=10, pady=5, sticky="w")
            self.distribution_vars[channel] = var

        tk.Label(place_frame, text="الموقع الجغرافي:", font=self.label_font, bg=self.box_bg).grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        self.location_entry = tk.Entry(place_frame, width=50, font=self.entry_font)
        self.location_entry.grid(row=2, column=1, columnspan=2, padx=10, pady=5, sticky="w")

        # الترويج (Promotion)
        promotion_frame = tk.LabelFrame(scrollable_frame, text="📢 الترويج (Promotion)",
                                      font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(promotion_frame, text="وسائل الترويج:", font=self.label_font, bg=self.box_bg).grid(
            row=0, column=0, sticky="ne", padx=10, pady=5)

        promotion_methods = [
            "وسائل التواصل الاجتماعي", "الإعلان المدفوع", "التسويق بالمحتوى",
            "العلاقات العامة", "الكلمة المنطوقة", "العروض والخصومات"
        ]
        self.promotion_vars = {}
        for i, method in enumerate(promotion_methods):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(promotion_frame, text=method, variable=var, bg=self.box_bg)
            cb.grid(row=i//3, column=i%3+1, padx=10, pady=5, sticky="w")
            self.promotion_vars[method] = var

        tk.Label(promotion_frame, text="الميزانية الشهرية للترويج:", font=self.label_font, bg=self.box_bg).grid(
            row=3, column=0, sticky="e", padx=10, pady=5)
        self.promotion_budget_entry = tk.Entry(promotion_frame, width=20, font=self.entry_font)
        self.promotion_budget_entry.grid(row=3, column=1, padx=10, pady=5, sticky="w")

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_production_requirements_tab(self):
        """إنشاء تبويب مستلزمات الإنتاج"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="5️⃣ مستلزمات الإنتاج")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المعدات والآلات
        equipment_frame = tk.LabelFrame(scrollable_frame, text="🔧 المعدات والآلات",
                                      font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        equipment_frame.pack(fill="x", padx=10, pady=10)

        # جدول المعدات
        equipment_table_frame = tk.Frame(equipment_frame, bg=self.box_bg)
        equipment_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        headers = ["المعدة/الآلة", "الكمية", "السعر الواحد", "الإجمالي"]
        for i, header in enumerate(headers):
            tk.Label(equipment_table_frame, text=header, font=self.label_font,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المعدات
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف للمعدات
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(equipment_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(equipment_table_frame, width=15, font=self.entry_font)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_equipment_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.equipment_entries.append(row_entries)

        # إجمالي المعدات
        tk.Label(equipment_table_frame, text="إجمالي المعدات:", font=self.label_font,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_equipment_label = tk.Label(equipment_table_frame, text="0", font=self.label_font,
                                            bg="yellow", relief="sunken")
        self.total_equipment_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

        # المواد الخام
        materials_frame = tk.LabelFrame(scrollable_frame, text="📦 المواد الخام",
                                      font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        materials_frame.pack(fill="x", padx=10, pady=10)

        # جدول المواد الخام
        materials_table_frame = tk.Frame(materials_frame, bg=self.box_bg)
        materials_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        for i, header in enumerate(headers):
            tk.Label(materials_table_frame, text=header, font=self.label_font,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المواد الخام
        self.materials_entries = []
        for row in range(1, 6):  # 5 صفوف للمواد الخام
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(materials_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(materials_table_frame, width=15, font=self.entry_font)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_materials_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.materials_entries.append(row_entries)

        # إجمالي المواد الخام
        tk.Label(materials_table_frame, text="إجمالي المواد الخام:", font=self.label_font,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_materials_label = tk.Label(materials_table_frame, text="0", font=self.label_font,
                                            bg="yellow", relief="sunken")
        self.total_materials_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

        # أزرار الحساب
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="حساب الإجماليات", command=self.calculate_production_totals,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_production_data,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def calculate_equipment_total(self, row):
        """حساب إجمالي المعدة في الصف المحدد"""
        try:
            quantity = float(self.equipment_entries[row][1].get() or 0)
            price = float(self.equipment_entries[row][2].get() or 0)
            total = quantity * price
            self.equipment_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except ValueError:
            pass

    def calculate_materials_total(self, row):
        """حساب إجمالي المادة الخام في الصف المحدد"""
        try:
            quantity = float(self.materials_entries[row][1].get() or 0)
            price = float(self.materials_entries[row][2].get() or 0)
            total = quantity * price
            self.materials_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except ValueError:
            pass

    def calculate_production_totals(self):
        """حساب الإجماليات الكلية"""
        # إجمالي المعدات
        equipment_total = 0
        for row in self.equipment_entries:
            try:
                equipment_total += float(row[3].cget("text"))
            except ValueError:
                pass
        self.total_equipment_label.config(text=f"{equipment_total:.2f}")

        # إجمالي المواد الخام
        materials_total = 0
        for row in self.materials_entries:
            try:
                materials_total += float(row[3].cget("text"))
            except ValueError:
                pass
        self.total_materials_label.config(text=f"{materials_total:.2f}")

    def clear_production_data(self):
        """مسح بيانات الإنتاج"""
        for row in self.equipment_entries:
            for i in range(3):  # مسح الأعمدة الثلاثة الأولى
                row[i].delete(0, tk.END)
            row[3].config(text="0")  # مسح عمود الإجمالي

        for row in self.materials_entries:
            for i in range(3):  # مسح الأعمدة الثلاثة الأولى
                row[i].delete(0, tk.END)
            row[3].config(text="0")  # مسح عمود الإجمالي

        self.total_equipment_label.config(text="0")
        self.total_materials_label.config(text="0")

    def create_startup_financial_tab(self):
        """إنشاء تبويب الدراسة المالية التأسيسية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="6️⃣ الدراسة المالية - التأسيسية")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تكاليف ما قبل التشغيل
        pre_operating_frame = tk.LabelFrame(scrollable_frame, text="💼 تكاليف ما قبل التشغيل",
                                          font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        pre_operating_frame.pack(fill="x", padx=10, pady=10)

        pre_operating_costs = [
            ("دراسة الجدوى", "feasibility_study"),
            ("الرسوم الحكومية والتراخيص", "licenses"),
            ("التأمين", "insurance"),
            ("الدعاية والإعلان الأولي", "initial_marketing"),
            ("التدريب", "training"),
            ("أخرى", "other_pre_operating")
        ]

        self.pre_operating_entries = {}
        for i, (label, key) in enumerate(pre_operating_costs):
            tk.Label(pre_operating_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(pre_operating_frame, width=20, font=self.entry_font)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entry.bind('<KeyRelease>', self.calculate_startup_totals)
            self.pre_operating_entries[key] = entry

        # إجمالي تكاليف ما قبل التشغيل
        tk.Label(pre_operating_frame, text="إجمالي تكاليف ما قبل التشغيل:",
                font=self.label_font, bg="#e0e0e0").grid(row=len(pre_operating_costs), column=0,
                                                        sticky="e", padx=10, pady=10)
        self.total_pre_operating_label = tk.Label(pre_operating_frame, text="0", font=self.label_font,
                                                bg="yellow", relief="sunken", width=15)
        self.total_pre_operating_label.grid(row=len(pre_operating_costs), column=1,
                                          padx=10, pady=10, sticky="w")

        # رأس المال الثابت
        fixed_capital_frame = tk.LabelFrame(scrollable_frame, text="🏢 رأس المال الثابت",
                                          font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        fixed_capital_frame.pack(fill="x", padx=10, pady=10)

        fixed_capital_costs = [
            ("الأراضي", "land"),
            ("المباني", "buildings"),
            ("المعدات والآلات", "equipment"),
            ("الأثاث والتجهيزات", "furniture"),
            ("وسائل النقل", "vehicles"),
            ("أخرى", "other_fixed")
        ]

        self.fixed_capital_entries = {}
        for i, (label, key) in enumerate(fixed_capital_costs):
            tk.Label(fixed_capital_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(fixed_capital_frame, width=20, font=self.entry_font)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entry.bind('<KeyRelease>', self.calculate_startup_totals)
            self.fixed_capital_entries[key] = entry

        # إجمالي رأس المال الثابت
        tk.Label(fixed_capital_frame, text="إجمالي رأس المال الثابت:",
                font=self.label_font, bg="#e0e0e0").grid(row=len(fixed_capital_costs), column=0,
                                                        sticky="e", padx=10, pady=10)
        self.total_fixed_capital_label = tk.Label(fixed_capital_frame, text="0", font=self.label_font,
                                                bg="yellow", relief="sunken", width=15)
        self.total_fixed_capital_label.grid(row=len(fixed_capital_costs), column=1,
                                          padx=10, pady=10, sticky="w")

        # الإجمالي العام
        total_frame = tk.LabelFrame(scrollable_frame, text="📊 الإجمالي العام",
                                  font=self.label_font, bg="#d4edda", relief="groove", bd=2)
        total_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(total_frame, text="إجمالي الاستثمار المطلوب:",
                font=("Arial", 14, "bold"), bg="#d4edda").grid(row=0, column=0, sticky="e", padx=10, pady=10)
        self.total_investment_label = tk.Label(total_frame, text="0",
                                             font=("Arial", 14, "bold"), bg="lightgreen",
                                             relief="sunken", width=20)
        self.total_investment_label.grid(row=0, column=1, padx=10, pady=10, sticky="w")

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="حساب الإجماليات", command=self.calculate_startup_totals,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_startup_data,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def calculate_startup_totals(self, event=None):
        """حساب إجماليات التكاليف التأسيسية"""
        # حساب تكاليف ما قبل التشغيل
        pre_operating_total = 0
        for entry in self.pre_operating_entries.values():
            try:
                pre_operating_total += float(entry.get() or 0)
            except ValueError:
                pass
        self.total_pre_operating_label.config(text=f"{pre_operating_total:.2f}")

        # حساب رأس المال الثابت
        fixed_capital_total = 0
        for entry in self.fixed_capital_entries.values():
            try:
                fixed_capital_total += float(entry.get() or 0)
            except ValueError:
                pass
        self.total_fixed_capital_label.config(text=f"{fixed_capital_total:.2f}")

        # الإجمالي العام
        total_investment = pre_operating_total + fixed_capital_total
        self.total_investment_label.config(text=f"{total_investment:.2f}")

    def clear_startup_data(self):
        """مسح بيانات التكاليف التأسيسية"""
        for entry in self.pre_operating_entries.values():
            entry.delete(0, tk.END)
        for entry in self.fixed_capital_entries.values():
            entry.delete(0, tk.END)

        self.total_pre_operating_label.config(text="0")
        self.total_fixed_capital_label.config(text="0")
        self.total_investment_label.config(text="0")

    def create_operational_financial_tab(self):
        """إنشاء تبويب الدراسة المالية التشغيلية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="7️⃣ الدراسة المالية - التشغيلية")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # التكاليف الثابتة الشهرية
        fixed_costs_frame = tk.LabelFrame(scrollable_frame, text="💰 التكاليف الثابتة الشهرية",
                                        font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        fixed_costs_frame.pack(fill="x", padx=10, pady=10)

        fixed_costs = [
            ("الإيجار", "rent"),
            ("الرواتب", "salaries"),
            ("الكهرباء", "electricity"),
            ("الماء", "water"),
            ("الهاتف والإنترنت", "communications"),
            ("التأمين", "insurance_monthly"),
            ("الصيانة", "maintenance"),
            ("أخرى", "other_fixed_monthly")
        ]

        self.fixed_costs_entries = {}
        for i, (label, key) in enumerate(fixed_costs):
            tk.Label(fixed_costs_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(fixed_costs_frame, width=20, font=self.entry_font)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entry.bind('<KeyRelease>', self.calculate_operational_totals)
            self.fixed_costs_entries[key] = entry

        # إجمالي التكاليف الثابتة
        tk.Label(fixed_costs_frame, text="إجمالي التكاليف الثابتة الشهرية:",
                font=self.label_font, bg="#e0e0e0").grid(row=len(fixed_costs), column=0,
                                                        sticky="e", padx=10, pady=10)
        self.total_fixed_costs_label = tk.Label(fixed_costs_frame, text="0", font=self.label_font,
                                              bg="yellow", relief="sunken", width=15)
        self.total_fixed_costs_label.grid(row=len(fixed_costs), column=1,
                                        padx=10, pady=10, sticky="w")

        # التكاليف المتغيرة
        variable_costs_frame = tk.LabelFrame(scrollable_frame, text="📈 التكاليف المتغيرة",
                                           font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        variable_costs_frame.pack(fill="x", padx=10, pady=10)

        variable_costs = [
            ("المواد الخام (شهرياً)", "raw_materials_monthly"),
            ("العمالة المؤقتة", "temporary_labor"),
            ("النقل والشحن", "transportation"),
            ("التسويق والإعلان", "marketing_monthly"),
            ("العمولات", "commissions"),
            ("أخرى", "other_variable")
        ]

        self.variable_costs_entries = {}
        for i, (label, key) in enumerate(variable_costs):
            tk.Label(variable_costs_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(variable_costs_frame, width=20, font=self.entry_font)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            entry.bind('<KeyRelease>', self.calculate_operational_totals)
            self.variable_costs_entries[key] = entry

        # إجمالي التكاليف المتغيرة
        tk.Label(variable_costs_frame, text="إجمالي التكاليف المتغيرة الشهرية:",
                font=self.label_font, bg="#e0e0e0").grid(row=len(variable_costs), column=0,
                                                        sticky="e", padx=10, pady=10)
        self.total_variable_costs_label = tk.Label(variable_costs_frame, text="0", font=self.label_font,
                                                 bg="yellow", relief="sunken", width=15)
        self.total_variable_costs_label.grid(row=len(variable_costs), column=1,
                                           padx=10, pady=10, sticky="w")

        # الإجمالي العام للتكاليف التشغيلية
        operational_total_frame = tk.LabelFrame(scrollable_frame, text="📊 إجمالي التكاليف التشغيلية",
                                              font=self.label_font, bg="#d4edda", relief="groove", bd=2)
        operational_total_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(operational_total_frame, text="إجمالي التكاليف التشغيلية الشهرية:",
                font=("Arial", 14, "bold"), bg="#d4edda").grid(row=0, column=0, sticky="e", padx=10, pady=10)
        self.total_operational_costs_label = tk.Label(operational_total_frame, text="0",
                                                     font=("Arial", 14, "bold"), bg="lightcoral",
                                                     relief="sunken", width=20)
        self.total_operational_costs_label.grid(row=0, column=1, padx=10, pady=10, sticky="w")

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="حساب الإجماليات", command=self.calculate_operational_totals,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_operational_data,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def calculate_operational_totals(self, event=None):
        """حساب إجماليات التكاليف التشغيلية"""
        # حساب التكاليف الثابتة
        fixed_total = 0
        for entry in self.fixed_costs_entries.values():
            try:
                fixed_total += float(entry.get() or 0)
            except ValueError:
                pass
        self.total_fixed_costs_label.config(text=f"{fixed_total:.2f}")

        # حساب التكاليف المتغيرة
        variable_total = 0
        for entry in self.variable_costs_entries.values():
            try:
                variable_total += float(entry.get() or 0)
            except ValueError:
                pass
        self.total_variable_costs_label.config(text=f"{variable_total:.2f}")

        # الإجمالي العام
        total_operational = fixed_total + variable_total
        self.total_operational_costs_label.config(text=f"{total_operational:.2f}")

    def clear_operational_data(self):
        """مسح بيانات التكاليف التشغيلية"""
        for entry in self.fixed_costs_entries.values():
            entry.delete(0, tk.END)
        for entry in self.variable_costs_entries.values():
            entry.delete(0, tk.END)

        self.total_fixed_costs_label.config(text="0")
        self.total_variable_costs_label.config(text="0")
        self.total_operational_costs_label.config(text="0")

    def create_financial_summary_tab(self):
        """إنشاء تبويب الدراسة المالية - ملخص شامل"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="8️⃣ الدراسة المالية - ملخص شامل")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # ملخص رأس المال
        capital_summary_frame = tk.LabelFrame(scrollable_frame, text="💼 ملخص رأس المال المطلوب",
                                            font=self.label_font, bg="#e8f4fd", relief="groove", bd=2)
        capital_summary_frame.pack(fill="x", padx=10, pady=10)

        # عرض الإجماليات من التبويبات السابقة
        tk.Label(capital_summary_frame, text="تكاليف ما قبل التشغيل:",
                font=self.label_font, bg="#e8f4fd").grid(row=0, column=0, sticky="e", padx=10, pady=5)
        self.summary_pre_operating_label = tk.Label(capital_summary_frame, text="0",
                                                  font=self.label_font, bg="white", relief="sunken", width=15)
        self.summary_pre_operating_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        tk.Label(capital_summary_frame, text="رأس المال الثابت:",
                font=self.label_font, bg="#e8f4fd").grid(row=1, column=0, sticky="e", padx=10, pady=5)
        self.summary_fixed_capital_label = tk.Label(capital_summary_frame, text="0",
                                                  font=self.label_font, bg="white", relief="sunken", width=15)
        self.summary_fixed_capital_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        tk.Label(capital_summary_frame, text="رأس المال العامل (3 أشهر):",
                font=self.label_font, bg="#e8f4fd").grid(row=2, column=0, sticky="e", padx=10, pady=5)
        self.summary_working_capital_label = tk.Label(capital_summary_frame, text="0",
                                                    font=self.label_font, bg="white", relief="sunken", width=15)
        self.summary_working_capital_label.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        # الإجمالي الكلي
        tk.Label(capital_summary_frame, text="إجمالي رأس المال المطلوب:",
                font=("Arial", 14, "bold"), bg="#e8f4fd").grid(row=3, column=0, sticky="e", padx=10, pady=15)
        self.summary_total_capital_label = tk.Label(capital_summary_frame, text="0",
                                                  font=("Arial", 14, "bold"), bg="lightgreen",
                                                  relief="sunken", width=20)
        self.summary_total_capital_label.grid(row=3, column=1, padx=10, pady=15, sticky="w")

        # تحليل نقطة التعادل
        breakeven_frame = tk.LabelFrame(scrollable_frame, text="⚖️ تحليل نقطة التعادل",
                                      font=self.label_font, bg="#fff2e8", relief="groove", bd=2)
        breakeven_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(breakeven_frame, text="سعر البيع للوحدة:",
                font=self.label_font, bg="#fff2e8").grid(row=0, column=0, sticky="e", padx=10, pady=5)
        self.unit_price_entry = tk.Entry(breakeven_frame, width=15, font=self.entry_font)
        self.unit_price_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        self.unit_price_entry.bind('<KeyRelease>', self.calculate_breakeven)

        tk.Label(breakeven_frame, text="التكلفة المتغيرة للوحدة:",
                font=self.label_font, bg="#fff2e8").grid(row=1, column=0, sticky="e", padx=10, pady=5)
        self.unit_variable_cost_entry = tk.Entry(breakeven_frame, width=15, font=self.entry_font)
        self.unit_variable_cost_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        self.unit_variable_cost_entry.bind('<KeyRelease>', self.calculate_breakeven)

        tk.Label(breakeven_frame, text="نقطة التعادل (وحدات):",
                font=("Arial", 12, "bold"), bg="#fff2e8").grid(row=2, column=0, sticky="e", padx=10, pady=10)
        self.breakeven_units_label = tk.Label(breakeven_frame, text="0",
                                            font=("Arial", 12, "bold"), bg="lightyellow",
                                            relief="sunken", width=15)
        self.breakeven_units_label.grid(row=2, column=1, padx=10, pady=10, sticky="w")

        tk.Label(breakeven_frame, text="نقطة التعادل (مبيعات):",
                font=("Arial", 12, "bold"), bg="#fff2e8").grid(row=3, column=0, sticky="e", padx=10, pady=5)
        self.breakeven_sales_label = tk.Label(breakeven_frame, text="0",
                                            font=("Arial", 12, "bold"), bg="lightyellow",
                                            relief="sunken", width=15)
        self.breakeven_sales_label.grid(row=3, column=1, padx=10, pady=5, sticky="w")

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="تحديث الملخص", command=self.update_financial_summary,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="حساب نقطة التعادل", command=self.calculate_breakeven,
                 bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def update_financial_summary(self):
        """تحديث ملخص الدراسة المالية"""
        try:
            # الحصول على القيم من التبويبات السابقة
            pre_operating = float(self.total_pre_operating_label.cget("text"))
            fixed_capital = float(self.total_fixed_capital_label.cget("text"))
            operational_monthly = float(self.total_operational_costs_label.cget("text"))
            working_capital = operational_monthly * 3  # رأس المال العامل لثلاثة أشهر

            # تحديث العرض
            self.summary_pre_operating_label.config(text=f"{pre_operating:.2f}")
            self.summary_fixed_capital_label.config(text=f"{fixed_capital:.2f}")
            self.summary_working_capital_label.config(text=f"{working_capital:.2f}")

            # الإجمالي
            total_capital = pre_operating + fixed_capital + working_capital
            self.summary_total_capital_label.config(text=f"{total_capital:.2f}")

        except (ValueError, AttributeError):
            messagebox.showwarning("تحذير", "يرجى إكمال البيانات في التبويبات السابقة أولاً")

    def calculate_breakeven(self, event=None):
        """حساب نقطة التعادل"""
        try:
            unit_price = float(self.unit_price_entry.get() or 0)
            unit_variable_cost = float(self.unit_variable_cost_entry.get() or 0)
            fixed_costs_monthly = float(self.total_fixed_costs_label.cget("text"))

            if unit_price > unit_variable_cost and unit_price > 0:
                contribution_margin = unit_price - unit_variable_cost
                breakeven_units = fixed_costs_monthly / contribution_margin
                breakeven_sales = breakeven_units * unit_price

                self.breakeven_units_label.config(text=f"{breakeven_units:.0f}")
                self.breakeven_sales_label.config(text=f"{breakeven_sales:.2f}")
            else:
                self.breakeven_units_label.config(text="غير محدد")
                self.breakeven_sales_label.config(text="غير محدد")

        except (ValueError, AttributeError):
            self.breakeven_units_label.config(text="0")
            self.breakeven_sales_label.config(text="0")

    def create_annual_revenue_tab(self):
        """إنشاء تبويب الإيرادات السنوية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="9️⃣ الإيرادات السنوية")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # جدول الإيرادات الشهرية
        revenue_frame = tk.LabelFrame(scrollable_frame, text="📈 الإيرادات الشهرية المتوقعة",
                                    font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        revenue_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الأشهر
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

        # إنشاء جدول الإيرادات
        self.monthly_revenue_entries = {}

        # عناوين الجدول
        tk.Label(revenue_frame, text="الشهر", font=self.label_font, bg="#e0e0e0", relief="ridge").grid(
            row=0, column=0, sticky="ew", padx=1, pady=1)
        tk.Label(revenue_frame, text="الإيرادات المتوقعة", font=self.label_font, bg="#e0e0e0", relief="ridge").grid(
            row=0, column=1, sticky="ew", padx=1, pady=1)

        # صفوف الأشهر
        for i, month in enumerate(months):
            tk.Label(revenue_frame, text=month, font=self.label_font, bg="white", relief="sunken").grid(
                row=i+1, column=0, sticky="ew", padx=1, pady=1)
            entry = tk.Entry(revenue_frame, width=20, font=self.entry_font)
            entry.grid(row=i+1, column=1, sticky="ew", padx=1, pady=1)
            entry.bind('<KeyRelease>', self.calculate_annual_revenue)
            self.monthly_revenue_entries[month] = entry

        # إجمالي الإيرادات السنوية
        tk.Label(revenue_frame, text="إجمالي الإيرادات السنوية:",
                font=("Arial", 14, "bold"), bg="#e0e0e0").grid(row=13, column=0, sticky="e", padx=5, pady=10)
        self.total_annual_revenue_label = tk.Label(revenue_frame, text="0",
                                                 font=("Arial", 14, "bold"), bg="lightgreen",
                                                 relief="sunken", width=20)
        self.total_annual_revenue_label.grid(row=13, column=1, sticky="ew", padx=1, pady=10)

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="حساب الإجمالي السنوي", command=self.calculate_annual_revenue,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="توزيع متساوي", command=self.distribute_revenue_equally,
                 bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_revenue_data,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_annual_profit_loss_tab(self):
        """إنشاء تبويب الربح والخسارة السنوي"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="🔟 الربح والخسارة السنوي")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # ملخص الربح والخسارة
        profit_loss_frame = tk.LabelFrame(scrollable_frame, text="📊 ملخص الربح والخسارة السنوي",
                                        font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
        profit_loss_frame.pack(fill="x", padx=10, pady=10)

        # الإيرادات
        tk.Label(profit_loss_frame, text="إجمالي الإيرادات السنوية:",
                font=self.label_font, bg=self.box_bg).grid(row=0, column=0, sticky="e", padx=10, pady=5)
        self.pl_total_revenue_label = tk.Label(profit_loss_frame, text="0",
                                             font=self.label_font, bg="lightgreen", relief="sunken", width=15)
        self.pl_total_revenue_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        # التكاليف
        tk.Label(profit_loss_frame, text="إجمالي التكاليف الثابتة السنوية:",
                font=self.label_font, bg=self.box_bg).grid(row=1, column=0, sticky="e", padx=10, pady=5)
        self.pl_total_fixed_costs_label = tk.Label(profit_loss_frame, text="0",
                                                 font=self.label_font, bg="lightcoral", relief="sunken", width=15)
        self.pl_total_fixed_costs_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        tk.Label(profit_loss_frame, text="إجمالي التكاليف المتغيرة السنوية:",
                font=self.label_font, bg=self.box_bg).grid(row=2, column=0, sticky="e", padx=10, pady=5)
        self.pl_total_variable_costs_label = tk.Label(profit_loss_frame, text="0",
                                                    font=self.label_font, bg="lightcoral", relief="sunken", width=15)
        self.pl_total_variable_costs_label.grid(row=2, column=1, padx=10, pady=5, sticky="w")

        # إجمالي التكاليف
        tk.Label(profit_loss_frame, text="إجمالي التكاليف:",
                font=("Arial", 12, "bold"), bg=self.box_bg).grid(row=3, column=0, sticky="e", padx=10, pady=10)
        self.pl_total_costs_label = tk.Label(profit_loss_frame, text="0",
                                           font=("Arial", 12, "bold"), bg="orange", relief="sunken", width=15)
        self.pl_total_costs_label.grid(row=3, column=1, padx=10, pady=10, sticky="w")

        # صافي الربح/الخسارة
        tk.Label(profit_loss_frame, text="صافي الربح/الخسارة:",
                font=("Arial", 14, "bold"), bg=self.box_bg).grid(row=4, column=0, sticky="e", padx=10, pady=15)
        self.net_profit_label = tk.Label(profit_loss_frame, text="0",
                                       font=("Arial", 14, "bold"), bg="yellow", relief="sunken", width=20)
        self.net_profit_label.grid(row=4, column=1, padx=10, pady=15, sticky="w")

        # نسبة الربح
        tk.Label(profit_loss_frame, text="نسبة الربح:",
                font=("Arial", 12, "bold"), bg=self.box_bg).grid(row=5, column=0, sticky="e", padx=10, pady=5)
        self.profit_margin_label = tk.Label(profit_loss_frame, text="0%",
                                          font=("Arial", 12, "bold"), bg="lightblue", relief="sunken", width=15)
        self.profit_margin_label.grid(row=5, column=1, padx=10, pady=5, sticky="w")

        # أزرار
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="حساب الربح والخسارة", command=self.calculate_profit_loss,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="تحديث من التبويبات السابقة", command=self.update_from_previous_tabs,
                 bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def calculate_annual_revenue(self, event=None):
        """حساب إجمالي الإيرادات السنوية"""
        total_revenue = 0
        for entry in self.monthly_revenue_entries.values():
            try:
                total_revenue += float(entry.get() or 0)
            except ValueError:
                pass
        self.total_annual_revenue_label.config(text=f"{total_revenue:.2f}")

    def distribute_revenue_equally(self):
        """توزيع الإيرادات بالتساوي على الأشهر"""
        try:
            total = float(self.total_annual_revenue_label.cget("text"))
            monthly_amount = total / 12
            for entry in self.monthly_revenue_entries.values():
                entry.delete(0, tk.END)
                entry.insert(0, f"{monthly_amount:.2f}")
        except ValueError:
            messagebox.showwarning("تحذير", "يرجى إدخال إجمالي الإيرادات أولاً")

    def clear_revenue_data(self):
        """مسح بيانات الإيرادات"""
        for entry in self.monthly_revenue_entries.values():
            entry.delete(0, tk.END)
        self.total_annual_revenue_label.config(text="0")

    def calculate_profit_loss(self):
        """حساب الربح والخسارة"""
        try:
            # الحصول على الإيرادات
            total_revenue = float(self.total_annual_revenue_label.cget("text"))

            # الحصول على التكاليف
            monthly_fixed = float(self.total_fixed_costs_label.cget("text"))
            monthly_variable = float(self.total_variable_costs_label.cget("text"))

            annual_fixed = monthly_fixed * 12
            annual_variable = monthly_variable * 12
            total_costs = annual_fixed + annual_variable

            # تحديث العرض
            self.pl_total_revenue_label.config(text=f"{total_revenue:.2f}")
            self.pl_total_fixed_costs_label.config(text=f"{annual_fixed:.2f}")
            self.pl_total_variable_costs_label.config(text=f"{annual_variable:.2f}")
            self.pl_total_costs_label.config(text=f"{total_costs:.2f}")

            # حساب صافي الربح
            net_profit = total_revenue - total_costs
            self.net_profit_label.config(text=f"{net_profit:.2f}")

            # حساب نسبة الربح
            if total_revenue > 0:
                profit_margin = (net_profit / total_revenue) * 100
                self.profit_margin_label.config(text=f"{profit_margin:.1f}%")
            else:
                self.profit_margin_label.config(text="0%")

            # تغيير لون صافي الربح حسب النتيجة
            if net_profit > 0:
                self.net_profit_label.config(bg="lightgreen")
            elif net_profit < 0:
                self.net_profit_label.config(bg="lightcoral")
            else:
                self.net_profit_label.config(bg="yellow")

        except (ValueError, AttributeError):
            messagebox.showwarning("تحذير", "يرجى إكمال البيانات في التبويبات السابقة")

    def update_from_previous_tabs(self):
        """تحديث البيانات من التبويبات السابقة"""
        self.calculate_annual_revenue()
        self.calculate_profit_loss()

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "تطبيق خطة العمل الشاملة\nالإصدار 1.0\n\nتطبيق شامل لإعداد خطط العمل")

if __name__ == "__main__":
    root = tk.Tk()
    app = BusinessPlanApp(root)
    root.mainloop()
