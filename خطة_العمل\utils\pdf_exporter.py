#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مُصدِّر PDF - تصدير خطط الأعمال إلى PDF
PDF Exporter - Export Business Plans to PDF

تصدير البيانات إلى ملفات PDF مع دعم العربية
"""

import os
from datetime import datetime
from tkinter import filedialog, messagebox

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

class PDFExporter:
    """مُصدِّر PDF للمشاريع"""
    
    def __init__(self):
        self.data_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        if not REPORTLAB_AVAILABLE:
            return
            
        try:
            # محاولة تسجيل خط عربي
            # يمكن إضافة ملفات خطوط عربية في مجلد assets
            font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets', 'arabic_font.ttf')
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                self.arabic_font = 'Helvetica'  # خط افتراضي
        except:
            self.arabic_font = 'Helvetica'
            
    def export_to_pdf(self, project_data, filename=None):
        """تصدير المشروع إلى PDF"""
        if not REPORTLAB_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة ReportLab غير مثبتة\nيرجى تثبيتها باستخدام: pip install reportlab")
            return False
            
        try:
            if not filename:
                filename = filedialog.asksaveasfilename(
                    title="تصدير إلى PDF",
                    defaultextension=".pdf",
                    filetypes=[("ملفات PDF", "*.pdf"), ("جميع الملفات", "*.*")],
                    initialdir=self.data_folder
                )
                
            if filename:
                self.create_pdf_document(project_data, filename)
                return True
                
        except Exception as e:
            raise Exception(f"خطأ في تصدير PDF: {str(e)}")
            
        return False
        
    def create_pdf_document(self, project_data, filename):
        """إنشاء مستند PDF"""
        # إنشاء المستند
        doc = SimpleDocTemplate(filename, pagesize=A4,
                               rightMargin=72, leftMargin=72,
                               topMargin=72, bottomMargin=18)
        
        # قائمة العناصر
        story = []
        
        # إعداد الأنماط
        styles = self.setup_styles()
        
        # إضافة العنوان الرئيسي
        story.append(Paragraph("خطة العمل التجاري", styles['Title']))
        story.append(Spacer(1, 12))
        
        # إضافة تاريخ الإنشاء
        creation_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        story.append(Paragraph(f"تاريخ الإنشاء: {creation_date}", styles['Normal']))
        story.append(Spacer(1, 20))
        
        # إضافة الأقسام
        sections = [
            ('personal_info', 'المعلومات الشخصية'),
            ('market_analysis', 'دراسة السوق'),
            ('swot_analysis', 'تحليل SWOT'),
            ('marketing_mix', 'المزيج التسويقي'),
            ('requirements', 'مستلزمات الإنتاج'),
            ('startup_costs', 'التكاليف التأسيسية'),
            ('operating_costs', 'التكاليف التشغيلية'),
            ('financial_summary', 'الملخص المالي'),
            ('annual_revenue', 'الإيرادات السنوية'),
            ('profit_loss', 'الربح والخسارة')
        ]
        
        for section_key, section_title in sections:
            if section_key in project_data:
                story.extend(self.create_section(section_title, project_data[section_key], styles))
                
        # بناء المستند
        doc.build(story)
        
    def setup_styles(self):
        """إعداد أنماط النص"""
        styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        styles.add(ParagraphStyle(
            name='Title',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # وسط
            fontName=self.arabic_font
        ))
        
        # نمط عنوان القسم
        styles.add(ParagraphStyle(
            name='SectionTitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=20,
            fontName=self.arabic_font,
            textColor=colors.darkblue
        ))
        
        # نمط النص العادي
        styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=styles['Normal'],
            fontSize=11,
            fontName=self.arabic_font,
            alignment=2  # يمين (للعربية)
        ))
        
        return styles
        
    def create_section(self, title, data, styles):
        """إنشاء قسم في PDF"""
        elements = []
        
        # عنوان القسم
        elements.append(Paragraph(title, styles['SectionTitle']))
        elements.append(Spacer(1, 12))
        
        if isinstance(data, dict):
            # إنشاء جدول للبيانات
            table_data = []
            for key, value in data.items():
                if isinstance(value, (str, int, float)):
                    table_data.append([str(key), str(value)])
                    
            if table_data:
                table = Table(table_data, colWidths=[2*inch, 4*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, 0), self.arabic_font),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(table)
        else:
            # نص عادي
            elements.append(Paragraph(str(data), styles['ArabicNormal']))
            
        elements.append(Spacer(1, 20))
        return elements
        
    def create_simple_pdf(self, project_data, filename):
        """إنشاء PDF بسيط بدون مكتبات خارجية"""
        try:
            # استخدام مكتبة weasyprint كبديل
            import weasyprint
            
            html_content = self.create_html_content(project_data)
            weasyprint.HTML(string=html_content).write_pdf(filename)
            return True
            
        except ImportError:
            # إنشاء ملف نصي كبديل
            txt_filename = filename.replace('.pdf', '.txt')
            with open(txt_filename, 'w', encoding='utf-8') as f:
                f.write("خطة العمل التجاري\n")
                f.write("=" * 50 + "\n\n")
                
                for section, data in project_data.items():
                    f.write(f"{section}:\n")
                    f.write("-" * 30 + "\n")
                    
                    if isinstance(data, dict):
                        for key, value in data.items():
                            f.write(f"{key}: {value}\n")
                    else:
                        f.write(f"{data}\n")
                        
                    f.write("\n")
                    
            messagebox.showinfo("تنبيه", f"تم إنشاء ملف نصي بدلاً من PDF:\n{txt_filename}")
            return True
            
    def create_html_content(self, project_data):
        """إنشاء محتوى HTML للتصدير"""
        html = """
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>خطة العمل التجاري</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #2c5282; text-align: center; }
                h2 { color: #4a5568; border-bottom: 2px solid #d69e2e; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <h1>خطة العمل التجاري</h1>
        """
        
        sections = {
            'personal_info': 'المعلومات الشخصية',
            'market_analysis': 'دراسة السوق',
            'swot_analysis': 'تحليل SWOT',
            'marketing_mix': 'المزيج التسويقي',
            'requirements': 'مستلزمات الإنتاج',
            'startup_costs': 'التكاليف التأسيسية',
            'operating_costs': 'التكاليف التشغيلية',
            'financial_summary': 'الملخص المالي',
            'annual_revenue': 'الإيرادات السنوية',
            'profit_loss': 'الربح والخسارة'
        }
        
        for section_key, section_title in sections.items():
            if section_key in project_data:
                html += f"<h2>{section_title}</h2>"
                
                data = project_data[section_key]
                if isinstance(data, dict):
                    html += "<table><tr><th>الحقل</th><th>القيمة</th></tr>"
                    for key, value in data.items():
                        html += f"<tr><td>{key}</td><td>{value}</td></tr>"
                    html += "</table>"
                else:
                    html += f"<p>{data}</p>"
                    
        html += "</body></html>"
        return html
