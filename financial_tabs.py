#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التبويبات المالية الإضافية لتطبيق خطة العمل
Additional Financial Tabs for Business Plan Application
"""

import tkinter as tk
from tkinter import ttk

def create_operational_financial_tab(self):
    """إنشاء تبويب الدراسة المالية التشغيلية"""
    tab = tk.Frame(self.notebook, bg="#f5f7fa")
    self.notebook.add(tab, text="7️⃣ الدراسة المالية - التشغيلية")
    
    # إطار قابل للتمرير
    canvas = tk.Canvas(tab, bg="#f5f7fa")
    scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # التكاليف الثابتة الشهرية
    fixed_costs_frame = tk.LabelFrame(scrollable_frame, text="💰 التكاليف الثابتة الشهرية", 
                                    font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
    fixed_costs_frame.pack(fill="x", padx=10, pady=10)
    
    fixed_costs = [
        ("الإيجار", "rent"),
        ("الرواتب", "salaries"),
        ("الكهرباء", "electricity"),
        ("الماء", "water"),
        ("الهاتف والإنترنت", "communications"),
        ("التأمين", "insurance_monthly"),
        ("الصيانة", "maintenance"),
        ("أخرى", "other_fixed_monthly")
    ]
    
    self.fixed_costs_entries = {}
    for i, (label, key) in enumerate(fixed_costs):
        tk.Label(fixed_costs_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
            row=i, column=0, sticky="e", padx=10, pady=5)
        entry = tk.Entry(fixed_costs_frame, width=20, font=self.entry_font)
        entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
        entry.bind('<KeyRelease>', self.calculate_operational_totals)
        self.fixed_costs_entries[key] = entry
    
    # إجمالي التكاليف الثابتة
    tk.Label(fixed_costs_frame, text="إجمالي التكاليف الثابتة الشهرية:", 
            font=self.label_font, bg="#e0e0e0").grid(row=len(fixed_costs), column=0, 
                                                    sticky="e", padx=10, pady=10)
    self.total_fixed_costs_label = tk.Label(fixed_costs_frame, text="0", font=self.label_font, 
                                          bg="yellow", relief="sunken", width=15)
    self.total_fixed_costs_label.grid(row=len(fixed_costs), column=1, 
                                    padx=10, pady=10, sticky="w")
    
    # التكاليف المتغيرة
    variable_costs_frame = tk.LabelFrame(scrollable_frame, text="📈 التكاليف المتغيرة", 
                                       font=self.label_font, bg=self.box_bg, relief="groove", bd=2)
    variable_costs_frame.pack(fill="x", padx=10, pady=10)
    
    variable_costs = [
        ("المواد الخام (شهرياً)", "raw_materials_monthly"),
        ("العمالة المؤقتة", "temporary_labor"),
        ("النقل والشحن", "transportation"),
        ("التسويق والإعلان", "marketing_monthly"),
        ("العمولات", "commissions"),
        ("أخرى", "other_variable")
    ]
    
    self.variable_costs_entries = {}
    for i, (label, key) in enumerate(variable_costs):
        tk.Label(variable_costs_frame, text=f"{label}:", font=self.label_font, bg=self.box_bg).grid(
            row=i, column=0, sticky="e", padx=10, pady=5)
        entry = tk.Entry(variable_costs_frame, width=20, font=self.entry_font)
        entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
        entry.bind('<KeyRelease>', self.calculate_operational_totals)
        self.variable_costs_entries[key] = entry
    
    # إجمالي التكاليف المتغيرة
    tk.Label(variable_costs_frame, text="إجمالي التكاليف المتغيرة الشهرية:", 
            font=self.label_font, bg="#e0e0e0").grid(row=len(variable_costs), column=0, 
                                                    sticky="e", padx=10, pady=10)
    self.total_variable_costs_label = tk.Label(variable_costs_frame, text="0", font=self.label_font, 
                                             bg="yellow", relief="sunken", width=15)
    self.total_variable_costs_label.grid(row=len(variable_costs), column=1, 
                                       padx=10, pady=10, sticky="w")
    
    # الإجمالي العام للتكاليف التشغيلية
    operational_total_frame = tk.LabelFrame(scrollable_frame, text="📊 إجمالي التكاليف التشغيلية", 
                                          font=self.label_font, bg="#d4edda", relief="groove", bd=2)
    operational_total_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Label(operational_total_frame, text="إجمالي التكاليف التشغيلية الشهرية:", 
            font=("Arial", 14, "bold"), bg="#d4edda").grid(row=0, column=0, sticky="e", padx=10, pady=10)
    self.total_operational_costs_label = tk.Label(operational_total_frame, text="0", 
                                                 font=("Arial", 14, "bold"), bg="lightcoral", 
                                                 relief="sunken", width=20)
    self.total_operational_costs_label.grid(row=0, column=1, padx=10, pady=10, sticky="w")
    
    # أزرار
    buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(buttons_frame, text="حساب الإجماليات", command=self.calculate_operational_totals,
             bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)
    
    tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_operational_data,
             bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)
    
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    return tab

def calculate_operational_totals(self, event=None):
    """حساب إجماليات التكاليف التشغيلية"""
    # حساب التكاليف الثابتة
    fixed_total = 0
    for entry in self.fixed_costs_entries.values():
        try:
            fixed_total += float(entry.get() or 0)
        except ValueError:
            pass
    self.total_fixed_costs_label.config(text=f"{fixed_total:.2f}")
    
    # حساب التكاليف المتغيرة
    variable_total = 0
    for entry in self.variable_costs_entries.values():
        try:
            variable_total += float(entry.get() or 0)
        except ValueError:
            pass
    self.total_variable_costs_label.config(text=f"{variable_total:.2f}")
    
    # الإجمالي العام
    total_operational = fixed_total + variable_total
    self.total_operational_costs_label.config(text=f"{total_operational:.2f}")

def clear_operational_data(self):
    """مسح بيانات التكاليف التشغيلية"""
    for entry in self.fixed_costs_entries.values():
        entry.delete(0, tk.END)
    for entry in self.variable_costs_entries.values():
        entry.delete(0, tk.END)
    
    self.total_fixed_costs_label.config(text="0")
    self.total_variable_costs_label.config(text="0")
    self.total_operational_costs_label.config(text="0")

def create_financial_summary_tab(self):
    """إنشاء تبويب الدراسة المالية - ملخص شامل"""
    tab = tk.Frame(self.notebook, bg="#f5f7fa")
    self.notebook.add(tab, text="8️⃣ الدراسة المالية - ملخص شامل")
    
    # إطار قابل للتمرير
    canvas = tk.Canvas(tab, bg="#f5f7fa")
    scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # ملخص رأس المال
    capital_summary_frame = tk.LabelFrame(scrollable_frame, text="💼 ملخص رأس المال المطلوب", 
                                        font=self.label_font, bg="#e8f4fd", relief="groove", bd=2)
    capital_summary_frame.pack(fill="x", padx=10, pady=10)
    
    # عرض الإجماليات من التبويبات السابقة
    tk.Label(capital_summary_frame, text="تكاليف ما قبل التشغيل:", 
            font=self.label_font, bg="#e8f4fd").grid(row=0, column=0, sticky="e", padx=10, pady=5)
    self.summary_pre_operating_label = tk.Label(capital_summary_frame, text="0", 
                                              font=self.label_font, bg="white", relief="sunken", width=15)
    self.summary_pre_operating_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")
    
    tk.Label(capital_summary_frame, text="رأس المال الثابت:", 
            font=self.label_font, bg="#e8f4fd").grid(row=1, column=0, sticky="e", padx=10, pady=5)
    self.summary_fixed_capital_label = tk.Label(capital_summary_frame, text="0", 
                                              font=self.label_font, bg="white", relief="sunken", width=15)
    self.summary_fixed_capital_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")
    
    tk.Label(capital_summary_frame, text="رأس المال العامل (3 أشهر):", 
            font=self.label_font, bg="#e8f4fd").grid(row=2, column=0, sticky="e", padx=10, pady=5)
    self.summary_working_capital_label = tk.Label(capital_summary_frame, text="0", 
                                                font=self.label_font, bg="white", relief="sunken", width=15)
    self.summary_working_capital_label.grid(row=2, column=1, padx=10, pady=5, sticky="w")
    
    # الإجمالي الكلي
    tk.Label(capital_summary_frame, text="إجمالي رأس المال المطلوب:", 
            font=("Arial", 14, "bold"), bg="#e8f4fd").grid(row=3, column=0, sticky="e", padx=10, pady=15)
    self.summary_total_capital_label = tk.Label(capital_summary_frame, text="0", 
                                              font=("Arial", 14, "bold"), bg="lightgreen", 
                                              relief="sunken", width=20)
    self.summary_total_capital_label.grid(row=3, column=1, padx=10, pady=15, sticky="w")
    
    # تحليل نقطة التعادل
    breakeven_frame = tk.LabelFrame(scrollable_frame, text="⚖️ تحليل نقطة التعادل", 
                                  font=self.label_font, bg="#fff2e8", relief="groove", bd=2)
    breakeven_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Label(breakeven_frame, text="سعر البيع للوحدة:", 
            font=self.label_font, bg="#fff2e8").grid(row=0, column=0, sticky="e", padx=10, pady=5)
    self.unit_price_entry = tk.Entry(breakeven_frame, width=15, font=self.entry_font)
    self.unit_price_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")
    self.unit_price_entry.bind('<KeyRelease>', self.calculate_breakeven)
    
    tk.Label(breakeven_frame, text="التكلفة المتغيرة للوحدة:", 
            font=self.label_font, bg="#fff2e8").grid(row=1, column=0, sticky="e", padx=10, pady=5)
    self.unit_variable_cost_entry = tk.Entry(breakeven_frame, width=15, font=self.entry_font)
    self.unit_variable_cost_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")
    self.unit_variable_cost_entry.bind('<KeyRelease>', self.calculate_breakeven)
    
    tk.Label(breakeven_frame, text="نقطة التعادل (وحدات):", 
            font=("Arial", 12, "bold"), bg="#fff2e8").grid(row=2, column=0, sticky="e", padx=10, pady=10)
    self.breakeven_units_label = tk.Label(breakeven_frame, text="0", 
                                        font=("Arial", 12, "bold"), bg="lightyellow", 
                                        relief="sunken", width=15)
    self.breakeven_units_label.grid(row=2, column=1, padx=10, pady=10, sticky="w")
    
    tk.Label(breakeven_frame, text="نقطة التعادل (مبيعات):", 
            font=("Arial", 12, "bold"), bg="#fff2e8").grid(row=3, column=0, sticky="e", padx=10, pady=5)
    self.breakeven_sales_label = tk.Label(breakeven_frame, text="0", 
                                        font=("Arial", 12, "bold"), bg="lightyellow", 
                                        relief="sunken", width=15)
    self.breakeven_sales_label.grid(row=3, column=1, padx=10, pady=5, sticky="w")
    
    # أزرار
    buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
    buttons_frame.pack(fill="x", padx=10, pady=10)
    
    tk.Button(buttons_frame, text="تحديث الملخص", command=self.update_financial_summary,
             bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)
    
    tk.Button(buttons_frame, text="حساب نقطة التعادل", command=self.calculate_breakeven,
             bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)
    
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    return tab
