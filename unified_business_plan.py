#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خطة العمل الموحدة - جميع التبويبات في واجهة واحدة
Unified Business Plan - All Tabs in One Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class UnifiedBusinessPlanApp:
    def __init__(self):
        # إعداد النافذة الرئيسية 
        self.root = tk.Tk() 
        self.root.title("خطة عمل المشروع") 
        self.root.geometry("1200x800") 
        self.root.configure(bg="#f5f7fa")  # خلفية رمادية فاتحة 
        
        # ألوان 
        self.primary_color = "#2563eb"  # أزرق ساطع 
        self.header_color = "#1e3a8a"   # أزرق داكن 
        self.box_bg = "#ffffff"         # أبيض 
        self.font_title = ("Arial", 18, "bold") 
        self.font_normal = ("Arial", 12) 
        self.font_small = ("Arial", 10)
        
        # تخزين البيانات
        self.data = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الموحدة"""
        # شريط القوائم
        self.create_menu()
        
        # العنوان الرئيسي
        header_frame = tk.Frame(self.root, bg=self.header_color, height=80)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="📋 خطة عمل المشروع الشاملة", 
                              font=self.font_title, fg="white", bg=self.header_color)
        title_label.pack(expand=True)
        
        # دفتر التبويبات الموحد
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء جميع التبويبات
        self.create_all_tabs()
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير PDF", command=self.export_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="حساب الإجماليات", command=self.calculate_totals)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_all_tabs(self):
        """إنشاء جميع التبويبات العشرة"""
        # التبويب 1: المعلومات الشخصية
        self.create_personal_info_tab()
        
        # التبويب 2: دراسة السوق والمنافسين
        self.create_market_analysis_tab()
        
        # التبويب 3: تحليل SWOT
        self.create_swot_analysis_tab()
        
        # التبويب 4: المزيج التسويقي
        self.create_marketing_mix_tab()
        
        # التبويب 5: مستلزمات الإنتاج
        self.create_production_requirements_tab()
        
        # التبويب 6: الدراسة المالية - التأسيسية
        self.create_startup_financial_tab()
        
        # التبويب 7: الدراسة المالية - التشغيلية
        self.create_operational_financial_tab()
        
        # التبويب 8: الدراسة المالية - ملخص شامل
        self.create_financial_summary_tab()
        
        # التبويب 9: الإيرادات السنوية
        self.create_annual_revenue_tab()
        
        # التبويب 10: الربح والخسارة السنوي
        self.create_annual_profit_loss_tab()
    
    def create_personal_info_tab(self):
        """التبويب 1: المعلومات الشخصية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="1️⃣ المعلومات الشخصية")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المعلومات الشخصية
        personal_frame = tk.LabelFrame(scrollable_frame, text="المعلومات الشخصية", 
                                     font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        personal_frame.pack(fill="x", padx=20, pady=10)
        
        # الحقول
        fields = [
            ("الاسم الكامل:", "full_name"),
            ("العمر:", "age"),
            ("المؤهل العلمي:", "education"),
            ("الخبرة السابقة:", "experience"),
            ("رقم الهاتف:", "phone"),
            ("البريد الإلكتروني:", "email")
        ]
        
        self.personal_entries = {}
        for i, (label, key) in enumerate(fields):
            tk.Label(personal_frame, text=label, font=self.font_normal, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(personal_frame, width=50, font=self.font_small)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            self.personal_entries[key] = entry
        
        # معلومات المشروع
        project_frame = tk.LabelFrame(scrollable_frame, text="معلومات المشروع", 
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        project_frame.pack(fill="x", padx=20, pady=10)
        
        # اسم المشروع
        tk.Label(project_frame, text="اسم المشروع:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.project_name_entry = tk.Entry(project_frame, width=50, font=self.font_small)
        self.project_name_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        # وصف المشروع
        tk.Label(project_frame, text="وصف المشروع:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="ne", padx=10, pady=5)
        self.project_desc_text = tk.Text(project_frame, width=60, height=4, font=self.font_small)
        self.project_desc_text.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        # نوع التمويل
        funding_frame = tk.LabelFrame(scrollable_frame, text="التمويل", 
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        funding_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(funding_frame, text="نوع التمويل:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        
        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = ["ذاتي", "قرض بنكي", "مستثمر", "مختلط"]
        for i, option in enumerate(funding_options):
            tk.Radiobutton(funding_frame, text=option, variable=self.funding_type, 
                          value=option, bg=self.box_bg, font=self.font_small).grid(row=0, column=i+1, padx=5)
        
        # مبلغ التمويل
        tk.Label(funding_frame, text="مبلغ التمويل المطلوب:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.funding_amount_entry = tk.Entry(funding_frame, width=20, font=self.font_small)
        self.funding_amount_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return tab
    
    def create_market_analysis_tab(self):
        """التبويب 2: دراسة السوق والمنافسين"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="2️⃣ دراسة السوق والمنافسين")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # القسم 1: المنتجات والخدمات
        products_frame = tk.LabelFrame(scrollable_frame, text="المنتجات والخدمات", 
                                     font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        products_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(products_frame, text="المنتجات:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.products_entry = tk.Entry(products_frame, width=80, font=self.font_small)
        self.products_entry.grid(row=0, column=1, padx=10, pady=5)
        
        tk.Label(products_frame, text="الخدمات:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.services_entry = tk.Entry(products_frame, width=80, font=self.font_small)
        self.services_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # القسم 2: المنافسون
        competitors_frame = tk.LabelFrame(scrollable_frame, text="تحليل المنافسين", 
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        competitors_frame.pack(fill="x", padx=20, pady=10)
        
        self.has_competitors = tk.StringVar(value="نعم")
        tk.Label(competitors_frame, text="هل يوجد منافسون؟", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        tk.Radiobutton(competitors_frame, text="نعم", variable=self.has_competitors, 
                      value="نعم", bg=self.box_bg, font=self.font_small).grid(row=0, column=1, sticky="w")
        tk.Radiobutton(competitors_frame, text="لا", variable=self.has_competitors, 
                      value="لا", bg=self.box_bg, font=self.font_small).grid(row=0, column=1, padx=60, sticky="w")
        
        tk.Label(competitors_frame, text="كم عددهم؟", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.competitor_count = tk.Entry(competitors_frame, width=10, font=self.font_small)
        self.competitor_count.grid(row=1, column=1, sticky="w", padx=10)
        
        tk.Label(competitors_frame, text="ما هي المنتجات المنافسة؟", font=self.font_normal, bg=self.box_bg).grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        self.comp_products_entry = tk.Entry(competitors_frame, width=80, font=self.font_small)
        self.comp_products_entry.grid(row=2, column=1, padx=10, pady=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return tab

    def create_swot_analysis_tab(self):
        """التبويب 3: تحليل SWOT"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="3️⃣ تحليل SWOT")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إطار رئيسي للتحليل
        main_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # نقاط القوة
        strengths_frame = tk.LabelFrame(main_frame, text="💪 نقاط القوة",
                                      font=self.font_normal, bg="#e8f5e8", relief="groove", bd=2)
        strengths_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        self.strengths_text = tk.Text(strengths_frame, width=40, height=8, font=self.font_small)
        self.strengths_text.pack(padx=10, pady=10, fill="both", expand=True)

        # نقاط الضعف
        weaknesses_frame = tk.LabelFrame(main_frame, text="⚠️ نقاط الضعف",
                                       font=self.font_normal, bg="#ffe8e8", relief="groove", bd=2)
        weaknesses_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        self.weaknesses_text = tk.Text(weaknesses_frame, width=40, height=8, font=self.font_small)
        self.weaknesses_text.pack(padx=10, pady=10, fill="both", expand=True)

        # الفرص
        opportunities_frame = tk.LabelFrame(main_frame, text="🌟 الفرص",
                                          font=self.font_normal, bg="#e8f0ff", relief="groove", bd=2)
        opportunities_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

        self.opportunities_text = tk.Text(opportunities_frame, width=40, height=8, font=self.font_small)
        self.opportunities_text.pack(padx=10, pady=10, fill="both", expand=True)

        # التهديدات
        threats_frame = tk.LabelFrame(main_frame, text="⚡ التهديدات",
                                    font=self.font_normal, bg="#fff0e8", relief="groove", bd=2)
        threats_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

        self.threats_text = tk.Text(threats_frame, width=40, height=8, font=self.font_small)
        self.threats_text.pack(padx=10, pady=10, fill="both", expand=True)

        # تكوين الشبكة
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_marketing_mix_tab(self):
        """التبويب 4: المزيج التسويقي"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="4️⃣ المزيج التسويقي")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المنتج
        product_frame = tk.LabelFrame(scrollable_frame, text="🛍️ المنتج",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        product_frame.pack(fill="x", padx=20, pady=10)

        self.product_text = tk.Text(product_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.product_text.pack(fill="x", padx=10, pady=10)

        # السعر
        price_frame = tk.LabelFrame(scrollable_frame, text="💰 السعر",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=20, pady=10)

        self.price_text = tk.Text(price_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.price_text.pack(fill="x", padx=10, pady=10)

        # المكان
        place_frame = tk.LabelFrame(scrollable_frame, text="📍 المكان",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        place_frame.pack(fill="x", padx=20, pady=10)

        self.place_text = tk.Text(place_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.place_text.pack(fill="x", padx=10, pady=10)

        # الترويج
        promotion_frame = tk.LabelFrame(scrollable_frame, text="📣 الترويج",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=20, pady=10)

        self.promotion_text = tk.Text(promotion_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.promotion_text.pack(fill="x", padx=10, pady=10)

        # الناس
        people_frame = tk.LabelFrame(scrollable_frame, text="👥 الناس",
                                   font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        people_frame.pack(fill="x", padx=20, pady=10)

        self.people_text = tk.Text(people_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.people_text.pack(fill="x", padx=10, pady=10)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_production_requirements_tab(self):
        """التبويب 5: مستلزمات الإنتاج"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="5️⃣ مستلزمات الإنتاج")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المعدات والآلات
        equipment_frame = tk.LabelFrame(scrollable_frame, text="🔧 المعدات والآلات",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        equipment_frame.pack(fill="x", padx=20, pady=10)

        # جدول المعدات
        equipment_table_frame = tk.Frame(equipment_frame, bg=self.box_bg)
        equipment_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        headers = ["المعدة/الآلة", "الكمية", "السعر الواحد", "الإجمالي"]
        for i, header in enumerate(headers):
            tk.Label(equipment_table_frame, text=header, font=self.font_normal,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المعدات
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف للمعدات
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(equipment_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(equipment_table_frame, width=15, font=self.font_small)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_equipment_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.equipment_entries.append(row_entries)

        # إجمالي المعدات
        tk.Label(equipment_table_frame, text="إجمالي المعدات:", font=self.font_normal,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_equipment_label = tk.Label(equipment_table_frame, text="0", font=self.font_normal,
                                            bg="yellow", relief="sunken")
        self.total_equipment_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

        # المواد الخام
        materials_frame = tk.LabelFrame(scrollable_frame, text="📦 المواد الخام",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        materials_frame.pack(fill="x", padx=20, pady=10)

        # جدول المواد الخام
        materials_table_frame = tk.Frame(materials_frame, bg=self.box_bg)
        materials_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        for i, header in enumerate(headers):
            tk.Label(materials_table_frame, text=header, font=self.font_normal,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المواد الخام
        self.materials_entries = []
        for row in range(1, 6):  # 5 صفوف للمواد الخام
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(materials_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(materials_table_frame, width=15, font=self.font_small)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_materials_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.materials_entries.append(row_entries)

        # إجمالي المواد الخام
        tk.Label(materials_table_frame, text="إجمالي المواد الخام:", font=self.font_normal,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_materials_label = tk.Label(materials_table_frame, text="0", font=self.font_normal,
                                            bg="yellow", relief="sunken")
        self.total_materials_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

        # أزرار الحساب
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=20, pady=10)

        tk.Button(buttons_frame, text="حساب الإجماليات", command=self.calculate_production_totals,
                 bg=self.primary_color, fg="white", font=self.font_normal).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="مسح البيانات", command=self.clear_production_data,
                 bg="#e74c3c", fg="white", font=self.font_normal).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_startup_financial_tab(self):
        """التبويب 6: الدراسة المالية - التأسيسية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="6️⃣ الدراسة المالية - التأسيسية")

        # إطار بسيط للتكاليف
        costs_frame = tk.LabelFrame(tab, text="التكاليف التأسيسية",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        costs_frame.pack(fill="x", padx=20, pady=20)

        self.startup_costs_text = tk.Text(costs_frame, height=10, font=self.font_small, wrap=tk.WORD)
        self.startup_costs_text.pack(fill="x", padx=10, pady=10)

        return tab

    def create_operational_financial_tab(self):
        """التبويب 7: الدراسة المالية - التشغيلية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="7️⃣ الدراسة المالية - التشغيلية")

        # إطار بسيط للتكاليف التشغيلية
        operational_frame = tk.LabelFrame(tab, text="التكاليف التشغيلية الشهرية",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        operational_frame.pack(fill="x", padx=20, pady=20)

        self.operational_costs_text = tk.Text(operational_frame, height=10, font=self.font_small, wrap=tk.WORD)
        self.operational_costs_text.pack(fill="x", padx=10, pady=10)

        return tab

    def create_financial_summary_tab(self):
        """التبويب 8: الدراسة المالية - ملخص شامل"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="8️⃣ الدراسة المالية - ملخص شامل")

        # إطار للملخص المالي
        summary_frame = tk.LabelFrame(tab, text="الملخص المالي الشامل",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        summary_frame.pack(fill="x", padx=20, pady=20)

        self.financial_summary_text = tk.Text(summary_frame, height=10, font=self.font_small, wrap=tk.WORD)
        self.financial_summary_text.pack(fill="x", padx=10, pady=10)

        return tab

    def create_annual_revenue_tab(self):
        """التبويب 9: الإيرادات السنوية"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="9️⃣ الإيرادات السنوية")

        # إطار للإيرادات
        revenue_frame = tk.LabelFrame(tab, text="الإيرادات السنوية المتوقعة",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        revenue_frame.pack(fill="x", padx=20, pady=20)

        self.annual_revenue_text = tk.Text(revenue_frame, height=10, font=self.font_small, wrap=tk.WORD)
        self.annual_revenue_text.pack(fill="x", padx=10, pady=10)

        return tab

    def create_annual_profit_loss_tab(self):
        """التبويب 10: الربح والخسارة السنوي"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="🔟 الربح والخسارة السنوي")

        # إطار للربح والخسارة
        profit_loss_frame = tk.LabelFrame(tab, text="تحليل الربح والخسارة السنوي",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        profit_loss_frame.pack(fill="x", padx=20, pady=20)

        self.profit_loss_text = tk.Text(profit_loss_frame, height=10, font=self.font_small, wrap=tk.WORD)
        self.profit_loss_text.pack(fill="x", padx=10, pady=10)

        return tab

    # الوظائف المساعدة
    def calculate_equipment_total(self, row):
        """حساب إجمالي المعدة في الصف المحدد"""
        try:
            quantity = float(self.equipment_entries[row][1].get() or 0)
            price = float(self.equipment_entries[row][2].get() or 0)
            total = quantity * price
            self.equipment_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except (ValueError, IndexError):
            pass

    def calculate_materials_total(self, row):
        """حساب إجمالي المادة الخام في الصف المحدد"""
        try:
            quantity = float(self.materials_entries[row][1].get() or 0)
            price = float(self.materials_entries[row][2].get() or 0)
            total = quantity * price
            self.materials_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except (ValueError, IndexError):
            pass

    def calculate_production_totals(self):
        """حساب الإجماليات الكلية"""
        # إجمالي المعدات
        equipment_total = 0
        for row in self.equipment_entries:
            try:
                equipment_total += float(row[3].cget("text"))
            except (ValueError, AttributeError):
                pass
        self.total_equipment_label.config(text=f"{equipment_total:.2f}")

        # إجمالي المواد الخام
        materials_total = 0
        for row in self.materials_entries:
            try:
                materials_total += float(row[3].cget("text"))
            except (ValueError, AttributeError):
                pass
        self.total_materials_label.config(text=f"{materials_total:.2f}")

    def clear_production_data(self):
        """مسح بيانات الإنتاج"""
        for row in self.equipment_entries:
            for i in range(3):  # مسح الأعمدة الثلاثة الأولى
                row[i].delete(0, tk.END)
            row[3].config(text="0")  # مسح عمود الإجمالي

        for row in self.materials_entries:
            for i in range(3):  # مسح الأعمدة الثلاثة الأولى
                row[i].delete(0, tk.END)
            row[3].config(text="0")  # مسح عمود الإجمالي

        self.total_equipment_label.config(text="0")
        self.total_materials_label.config(text="0")

    def calculate_totals(self):
        """حساب جميع الإجماليات"""
        self.calculate_production_totals()
        messagebox.showinfo("تم الحساب", "تم حساب جميع الإجماليات بنجاح")

    def new_project(self):
        """مشروع جديد"""
        result = messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.")
        if result:
            self.clear_all_data()

    def clear_all_data(self):
        """مسح جميع البيانات"""
        # مسح المعلومات الشخصية
        for entry in self.personal_entries.values():
            entry.delete(0, tk.END)

        self.project_name_entry.delete(0, tk.END)
        self.project_desc_text.delete(1.0, tk.END)
        self.funding_amount_entry.delete(0, tk.END)
        self.funding_type.set("ذاتي")

        # مسح دراسة السوق
        self.products_entry.delete(0, tk.END)
        self.services_entry.delete(0, tk.END)
        self.competitor_count.delete(0, tk.END)
        self.comp_products_entry.delete(0, tk.END)
        self.has_competitors.set("نعم")

        # مسح تحليل SWOT
        self.strengths_text.delete(1.0, tk.END)
        self.weaknesses_text.delete(1.0, tk.END)
        self.opportunities_text.delete(1.0, tk.END)
        self.threats_text.delete(1.0, tk.END)

        # مسح المزيج التسويقي
        self.product_text.delete(1.0, tk.END)
        self.price_text.delete(1.0, tk.END)
        self.place_text.delete(1.0, tk.END)
        self.promotion_text.delete(1.0, tk.END)
        self.people_text.delete(1.0, tk.END)

        # مسح بيانات الإنتاج
        self.clear_production_data()

        # مسح التبويبات المالية
        self.startup_costs_text.delete(1.0, tk.END)
        self.operational_costs_text.delete(1.0, tk.END)
        self.financial_summary_text.delete(1.0, tk.END)
        self.annual_revenue_text.delete(1.0, tk.END)
        self.profit_loss_text.delete(1.0, tk.END)

        messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def add_examples(self):
        """إضافة أمثلة للبيانات"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة للبيانات؟\nسيتم مسح البيانات الحالية.")
        if result:
            self.clear_all_data()

            # أمثلة المعلومات الشخصية
            self.personal_entries["full_name"].insert(0, "أحمد محمد علي")
            self.personal_entries["age"].insert(0, "30")
            self.personal_entries["education"].insert(0, "بكالوريوس إدارة أعمال")
            self.personal_entries["experience"].insert(0, "5 سنوات في مجال التسويق")
            self.personal_entries["phone"].insert(0, "0501234567")
            self.personal_entries["email"].insert(0, "<EMAIL>")

            self.project_name_entry.insert(0, "مشروع متجر إلكتروني للمنتجات المحلية")
            self.project_desc_text.insert(1.0, "متجر إلكتروني متخصص في بيع المنتجات المحلية والحرفية بجودة عالية وأسعار تنافسية")
            self.funding_amount_entry.insert(0, "100000")

            # أمثلة دراسة السوق
            self.products_entry.insert(0, "منتجات حرفية، أطعمة محلية، ملابس تراثية")
            self.services_entry.insert(0, "خدمة التوصيل، خدمة العملاء، ضمان الجودة")
            self.competitor_count.insert(0, "3")
            self.comp_products_entry.insert(0, "متاجر إلكترونية أخرى، محلات تقليدية")

            # أمثلة تحليل SWOT
            self.strengths_text.insert(1.0, "• خبرة في التسويق\n• شبكة علاقات واسعة\n• فهم عميق للسوق المحلي\n• منتجات عالية الجودة")
            self.weaknesses_text.insert(1.0, "• رأس مال محدود\n• نقص في الخبرة التقنية\n• فريق عمل صغير\n• عدم وجود علامة تجارية معروفة")
            self.opportunities_text.insert(1.0, "• نمو التجارة الإلكترونية\n• دعم حكومي للمشاريع الصغيرة\n• زيادة الاهتمام بالمنتجات المحلية\n• توفر منصات التسويق الرقمي")
            self.threats_text.insert(1.0, "• منافسة شديدة\n• تقلبات اقتصادية\n• تغيير في سلوك المستهلكين\n• مشاكل في سلسلة التوريد")

            # أمثلة المزيج التسويقي
            self.product_text.insert(1.0, "منتجات حرفية أصيلة عالية الجودة مع ضمان الأصالة والجودة")
            self.price_text.insert(1.0, "أسعار تنافسية مع خصومات للعملاء الدائمين وعروض موسمية")
            self.place_text.insert(1.0, "متجر إلكتروني، وسائل التواصل الاجتماعي، معارض محلية")
            self.promotion_text.insert(1.0, "التسويق عبر وسائل التواصل الاجتماعي، إعلانات مدفوعة، تسويق بالمحتوى")
            self.people_text.insert(1.0, "فريق متخصص في خدمة العملاء، موردين موثوقين، عملاء مستهدفين من محبي المنتجات المحلية")

            messagebox.showinfo("تم الإضافة", "تم إضافة الأمثلة بنجاح")

    def open_project(self):
        """فتح مشروع"""
        filename = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                messagebox.showinfo("نجح", "تم فتح المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        filename = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                # جمع البيانات
                self.data = {
                    'timestamp': datetime.now().isoformat(),
                    'project_name': self.project_name_entry.get(),
                    'project_description': self.project_desc_text.get(1.0, tk.END).strip()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        messagebox.showinfo("قريباً", "ميزة تصدير PDF ستكون متاحة قريباً")

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📋 دليل استخدام تطبيق خطة العمل

        🎯 كيفية الاستخدام:
        1. املأ البيانات في كل تبويب بالتفصيل
        2. استخدم أزرار الحساب للحصول على النتائج
        3. احفظ مشروعك بانتظام
        4. استخدم الأمثلة للتعلم

        💡 نصائح:
        • ابدأ بالمعلومات الشخصية
        • أكمل دراسة السوق بعناية
        • استخدم تحليل SWOT للتخطيط
        • راجع الأرقام المالية بدقة
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "تطبيق خطة العمل الموحدة\nالإصدار 1.0\n\nتطبيق شامل لإعداد خطط العمل")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = UnifiedBusinessPlanApp()
    app.run()
