#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تخطيط المشاريع الشامل
Comprehensive Project Planning System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class ComprehensiveProjectPlanner:
    def __init__(self):
        # إعداد النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("نظام تخطيط المشاريع الشامل")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f5f7fa")

        # ألوان وخطوط
        self.primary_color = "#2563eb"
        self.header_color = "#1e3a8a"
        self.box_bg = "#ffffff"
        self.font_title = ("Arial", 18, "bold")
        self.font_normal = ("Arial", 12)
        self.font_small = ("Arial", 10)
        
        # تخزين البيانات
        self.data = {}
        
        # الأقسام الرسمية حسب خطة العمل
        self.sections = [
            ("1️⃣", "المعلومات الشخصية + وصف المشروع"),
            ("2️⃣", "دراسة السوق والمنافسين"),
            ("3️⃣", "تحليل SWOT"),
            ("4️⃣", "المزيج التسويقي"),
            ("5️⃣", "مستلزمات الإنتاج"),
            ("6️⃣", "الدراسة المالية – التأسيسية والثابتة"),
            ("7️⃣", "الدراسة المالية – التشغيلية"),
            ("8️⃣", "الدراسة المالية – ملخص شامل"),
            ("9️⃣", "الإيرادات السنوية"),
            ("🔟", "الربح والخسارة السنوي"),
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        self.create_menu()
        
        # --- رأس الصفحة ---
        header_frame = tk.Frame(self.root, bg="#f5f7fa")
        header_frame.pack(fill="x", padx=20, pady=(10, 0))

        tk.Label(header_frame, text="نظام تخطيط المشاريع الشامل", font=self.font_title, 
                bg="#f5f7fa", fg=self.header_color).pack(anchor="center")
        tk.Label(header_frame, text="أداة متكاملة لتخطيط وتحليل المشاريع", font=self.font_small, 
                bg="#f5f7fa").pack(anchor="center", pady=5)

        # --- إطار رئيسي مقسم إلى عمودين ---
        main_frame = tk.Frame(self.root, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # القائمة الجانبية اليسرى
        self.sidebar = tk.Frame(main_frame, bg="#f5f7fa", width=280)
        self.sidebar.pack(side="left", fill="y")
        self.sidebar.pack_propagate(False)

        # منطقة المحتوى
        self.content = tk.Frame(main_frame, bg="#ffffff")
        self.content.pack(side="right", fill="both", expand=True, padx=(10, 0))

        # دالة عرض القسم المختار
        self.selected_section = tk.StringVar(value="المعلومات الشخصية + وصف المشروع")
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # عرض القسم الأول تلقائيًا
        self.show_section("المعلومات الشخصية + وصف المشروع")
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير PDF", command=self.export_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="حساب الإجماليات", command=self.calculate_totals)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        # عنوان القائمة
        sidebar_title = tk.Label(self.sidebar, text="📋 أقسام خطة العمل", 
                                font=("Arial", 14, "bold"), bg="#f5f7fa", fg=self.header_color)
        sidebar_title.pack(pady=(0, 10))
        
        # أزرار القائمة الجانبية
        for icon, name in self.sections:
            btn = tk.Radiobutton(
                self.sidebar, text=f"{icon} {name}", variable=self.selected_section, value=name,
                indicatoron=0, command=lambda n=name: self.show_section(n),
                font=self.font_normal, bg="#f5f7fa", fg="#000", activebackground="#e0e7ff",
                width=35, pady=8, anchor="w", relief="flat", bd=0
            )
            btn.pack(anchor="w", fill="x", padx=5, pady=2)
            
        # إضافة مساحة فارغة
        tk.Frame(self.sidebar, bg="#f5f7fa", height=20).pack()
        
        # أزرار سريعة
        quick_buttons_frame = tk.LabelFrame(self.sidebar, text="أدوات سريعة", 
                                          font=self.font_normal, bg="#f5f7fa")
        quick_buttons_frame.pack(fill="x", padx=5, pady=10)
        
        tk.Button(quick_buttons_frame, text="💾 حفظ سريع", command=self.quick_save,
                 bg=self.primary_color, fg="white", font=self.font_small).pack(fill="x", pady=2)
        
        tk.Button(quick_buttons_frame, text="🔄 مسح القسم", command=self.clear_current_section,
                 bg="#e74c3c", fg="white", font=self.font_small).pack(fill="x", pady=2)
        
        tk.Button(quick_buttons_frame, text="💡 أمثلة", command=self.add_examples,
                 bg="#f39c12", fg="white", font=self.font_small).pack(fill="x", pady=2)

    def show_section(self, name):
        """عرض القسم المختار"""
        self.selected_section.set(name)
        for widget in self.content.winfo_children():
            widget.destroy()
            
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(self.content, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self.content, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # عنوان القسم
        section_title = tk.Label(scrollable_frame, text=f"📄 {name}", font=self.font_title, 
                               bg="#ffffff", fg=self.header_color)
        section_title.pack(pady=(20, 30))
        
        # محتوى القسم حسب النوع
        if name == "المعلومات الشخصية + وصف المشروع":
            self.create_personal_info_content(scrollable_frame)
        elif name == "دراسة السوق والمنافسين":
            self.create_market_analysis_content(scrollable_frame)
        elif name == "تحليل SWOT":
            self.create_swot_analysis_content(scrollable_frame)
        elif name == "المزيج التسويقي":
            self.create_marketing_mix_content(scrollable_frame)
        elif name == "مستلزمات الإنتاج":
            self.create_production_requirements_content(scrollable_frame)
        elif name == "الدراسة المالية – التأسيسية والثابتة":
            self.create_startup_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – التشغيلية":
            self.create_operational_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – ملخص شامل":
            self.create_financial_summary_content(scrollable_frame)
        elif name == "الإيرادات السنوية":
            self.create_annual_revenue_content(scrollable_frame)
        elif name == "الربح والخسارة السنوي":
            self.create_annual_profit_loss_content(scrollable_frame)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_personal_info_content(self, parent):
        """محتوى قسم المعلومات الشخصية"""
        # المعلومات الشخصية
        personal_frame = tk.LabelFrame(parent, text="المعلومات الشخصية", 
                                     font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        personal_frame.pack(fill="x", padx=20, pady=10)
        
        # الحقول
        fields = [
            ("الاسم الكامل:", "full_name"),
            ("العمر:", "age"),
            ("المؤهل العلمي:", "education"),
            ("الخبرة السابقة:", "experience"),
            ("رقم الهاتف:", "phone"),
            ("البريد الإلكتروني:", "email")
        ]
        
        self.personal_entries = {}
        for i, (label, key) in enumerate(fields):
            tk.Label(personal_frame, text=label, font=self.font_normal, bg=self.box_bg).grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            entry = tk.Entry(personal_frame, width=50, font=self.font_small)
            entry.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            self.personal_entries[key] = entry
        
        # معلومات المشروع
        project_frame = tk.LabelFrame(parent, text="معلومات المشروع", 
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        project_frame.pack(fill="x", padx=20, pady=10)
        
        # اسم المشروع
        tk.Label(project_frame, text="اسم المشروع:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.project_name_entry = tk.Entry(project_frame, width=50, font=self.font_small)
        self.project_name_entry.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        # وصف المشروع
        tk.Label(project_frame, text="وصف المشروع:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="ne", padx=10, pady=5)
        self.project_desc_text = tk.Text(project_frame, width=60, height=4, font=self.font_small)
        self.project_desc_text.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        # نوع التمويل
        funding_frame = tk.LabelFrame(parent, text="التمويل", 
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        funding_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(funding_frame, text="نوع التمويل:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        
        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = ["ذاتي", "قرض بنكي", "مستثمر", "مختلط"]
        for i, option in enumerate(funding_options):
            tk.Radiobutton(funding_frame, text=option, variable=self.funding_type, 
                          value=option, bg=self.box_bg, font=self.font_small).grid(row=0, column=i+1, padx=5)
        
        # مبلغ التمويل
        tk.Label(funding_frame, text="مبلغ التمويل المطلوب:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.funding_amount_entry = tk.Entry(funding_frame, width=20, font=self.font_small)
        self.funding_amount_entry.grid(row=1, column=1, padx=10, pady=5, sticky="w")

    def create_market_analysis_content(self, parent):
        """محتوى قسم دراسة السوق"""
        # المنتجات والخدمات
        products_frame = tk.LabelFrame(parent, text="المنتجات والخدمات",
                                     font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        products_frame.pack(fill="x", padx=20, pady=10)

        tk.Label(products_frame, text="المنتجات:", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.products_entry = tk.Entry(products_frame, width=80, font=self.font_small)
        self.products_entry.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(products_frame, text="الخدمات:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.services_entry = tk.Entry(products_frame, width=80, font=self.font_small)
        self.services_entry.grid(row=1, column=1, padx=10, pady=5)

        # تحليل المنافسين
        competitors_frame = tk.LabelFrame(parent, text="تحليل المنافسين",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        competitors_frame.pack(fill="x", padx=20, pady=10)

        self.has_competitors = tk.StringVar(value="نعم")
        tk.Label(competitors_frame, text="هل يوجد منافسون؟", font=self.font_normal, bg=self.box_bg).grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        tk.Radiobutton(competitors_frame, text="نعم", variable=self.has_competitors,
                      value="نعم", bg=self.box_bg, font=self.font_small).grid(row=0, column=1, sticky="w")
        tk.Radiobutton(competitors_frame, text="لا", variable=self.has_competitors,
                      value="لا", bg=self.box_bg, font=self.font_small).grid(row=0, column=1, padx=60, sticky="w")

        tk.Label(competitors_frame, text="عدد المنافسين:", font=self.font_normal, bg=self.box_bg).grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.competitor_count = tk.Entry(competitors_frame, width=10, font=self.font_small)
        self.competitor_count.grid(row=1, column=1, sticky="w", padx=10)

        tk.Label(competitors_frame, text="المنتجات المنافسة:", font=self.font_normal, bg=self.box_bg).grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        self.comp_products_entry = tk.Entry(competitors_frame, width=80, font=self.font_small)
        self.comp_products_entry.grid(row=2, column=1, padx=10, pady=5)

    def create_swot_analysis_content(self, parent):
        """محتوى قسم تحليل SWOT"""
        # إطار رئيسي للتحليل
        main_frame = tk.Frame(parent, bg=self.box_bg)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # نقاط القوة
        strengths_frame = tk.LabelFrame(main_frame, text="💪 نقاط القوة",
                                      font=self.font_normal, bg="#e8f5e8", relief="groove", bd=2)
        strengths_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        self.strengths_text = tk.Text(strengths_frame, width=40, height=8, font=self.font_small)
        self.strengths_text.pack(padx=10, pady=10, fill="both", expand=True)

        # نقاط الضعف
        weaknesses_frame = tk.LabelFrame(main_frame, text="⚠️ نقاط الضعف",
                                       font=self.font_normal, bg="#ffe8e8", relief="groove", bd=2)
        weaknesses_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        self.weaknesses_text = tk.Text(weaknesses_frame, width=40, height=8, font=self.font_small)
        self.weaknesses_text.pack(padx=10, pady=10, fill="both", expand=True)

        # الفرص
        opportunities_frame = tk.LabelFrame(main_frame, text="🌟 الفرص",
                                          font=self.font_normal, bg="#e8f0ff", relief="groove", bd=2)
        opportunities_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

        self.opportunities_text = tk.Text(opportunities_frame, width=40, height=8, font=self.font_small)
        self.opportunities_text.pack(padx=10, pady=10, fill="both", expand=True)

        # التهديدات
        threats_frame = tk.LabelFrame(main_frame, text="⚡ التهديدات",
                                    font=self.font_normal, bg="#fff0e8", relief="groove", bd=2)
        threats_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

        self.threats_text = tk.Text(threats_frame, width=40, height=8, font=self.font_small)
        self.threats_text.pack(padx=10, pady=10, fill="both", expand=True)

        # تكوين الشبكة
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)

    def create_marketing_mix_content(self, parent):
        """محتوى قسم المزيج التسويقي"""
        # المنتج
        product_frame = tk.LabelFrame(parent, text="🛍️ المنتج",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        product_frame.pack(fill="x", padx=20, pady=10)

        self.product_text = tk.Text(product_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.product_text.pack(fill="x", padx=10, pady=10)

        # السعر
        price_frame = tk.LabelFrame(parent, text="💰 السعر",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=20, pady=10)

        self.price_text = tk.Text(price_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.price_text.pack(fill="x", padx=10, pady=10)

        # المكان
        place_frame = tk.LabelFrame(parent, text="📍 المكان",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        place_frame.pack(fill="x", padx=20, pady=10)

        self.place_text = tk.Text(place_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.place_text.pack(fill="x", padx=10, pady=10)

        # الترويج
        promotion_frame = tk.LabelFrame(parent, text="📣 الترويج",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=20, pady=10)

        self.promotion_text = tk.Text(promotion_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.promotion_text.pack(fill="x", padx=10, pady=10)

        # الناس
        people_frame = tk.LabelFrame(parent, text="👥 الناس",
                                   font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        people_frame.pack(fill="x", padx=20, pady=10)

        self.people_text = tk.Text(people_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.people_text.pack(fill="x", padx=10, pady=10)

    def create_production_requirements_content(self, parent):
        """محتوى قسم مستلزمات الإنتاج"""
        # المعدات والآلات
        equipment_frame = tk.LabelFrame(parent, text="🔧 المعدات والآلات",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        equipment_frame.pack(fill="x", padx=20, pady=10)

        # جدول المعدات
        equipment_table_frame = tk.Frame(equipment_frame, bg=self.box_bg)
        equipment_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        headers = ["المعدة/الآلة", "الكمية", "السعر الواحد", "الإجمالي"]
        for i, header in enumerate(headers):
            tk.Label(equipment_table_frame, text=header, font=self.font_normal,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المعدات
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف للمعدات
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(equipment_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(equipment_table_frame, width=15, font=self.font_small)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_equipment_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.equipment_entries.append(row_entries)

        # إجمالي المعدات
        tk.Label(equipment_table_frame, text="إجمالي المعدات:", font=self.font_normal,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_equipment_label = tk.Label(equipment_table_frame, text="0", font=self.font_normal,
                                            bg="yellow", relief="sunken")
        self.total_equipment_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

    def create_startup_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التأسيسية"""
        costs_frame = tk.LabelFrame(parent, text="التكاليف التأسيسية والثابتة",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        costs_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.startup_costs_text = tk.Text(costs_frame, font=self.font_small, wrap=tk.WORD)
        self.startup_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_operational_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التشغيلية"""
        operational_frame = tk.LabelFrame(parent, text="التكاليف التشغيلية الشهرية",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        operational_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.operational_costs_text = tk.Text(operational_frame, font=self.font_small, wrap=tk.WORD)
        self.operational_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_financial_summary_content(self, parent):
        """محتوى قسم الدراسة المالية الملخص الشامل"""
        summary_frame = tk.LabelFrame(parent, text="الملخص المالي الشامل",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        summary_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.financial_summary_text = tk.Text(summary_frame, font=self.font_small, wrap=tk.WORD)
        self.financial_summary_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_revenue_content(self, parent):
        """محتوى قسم الإيرادات السنوية"""
        revenue_frame = tk.LabelFrame(parent, text="الإيرادات السنوية المتوقعة",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        revenue_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.annual_revenue_text = tk.Text(revenue_frame, font=self.font_small, wrap=tk.WORD)
        self.annual_revenue_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_profit_loss_content(self, parent):
        """محتوى قسم الربح والخسارة السنوي"""
        profit_loss_frame = tk.LabelFrame(parent, text="تحليل الربح والخسارة السنوي",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        profit_loss_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.profit_loss_text = tk.Text(profit_loss_frame, font=self.font_small, wrap=tk.WORD)
        self.profit_loss_text.pack(fill="both", expand=True, padx=10, pady=10)

    # الوظائف المساعدة
    def calculate_equipment_total(self, row):
        """حساب إجمالي المعدة في الصف المحدد"""
        try:
            quantity = float(self.equipment_entries[row][1].get() or 0)
            price = float(self.equipment_entries[row][2].get() or 0)
            total = quantity * price
            self.equipment_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except (ValueError, IndexError):
            pass

    def calculate_production_totals(self):
        """حساب الإجماليات الكلية"""
        equipment_total = 0
        for row in self.equipment_entries:
            try:
                equipment_total += float(row[3].cget("text"))
            except (ValueError, AttributeError):
                pass
        self.total_equipment_label.config(text=f"{equipment_total:.2f}")

    def calculate_totals(self):
        """حساب جميع الإجماليات"""
        self.calculate_production_totals()
        messagebox.showinfo("تم الحساب", "تم حساب جميع الإجماليات بنجاح")

    def quick_save(self):
        """حفظ سريع"""
        messagebox.showinfo("حفظ سريع", "تم الحفظ السريع بنجاح")

    def clear_current_section(self):
        """مسح القسم الحالي"""
        current = self.selected_section.get()
        result = messagebox.askyesno("تأكيد المسح", f"هل تريد مسح بيانات قسم:\n{current}؟")
        if result:
            # إعادة عرض القسم لمسح البيانات
            self.show_section(current)
            messagebox.showinfo("تم المسح", f"تم مسح بيانات قسم {current}")

    def new_project(self):
        """مشروع جديد"""
        result = messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.")
        if result:
            self.clear_all_data()

    def clear_all_data(self):
        """مسح جميع البيانات"""
        # إعادة عرض القسم الأول
        self.show_section("المعلومات الشخصية + وصف المشروع")
        messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def add_examples(self):
        """إضافة أمثلة للبيانات"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة للبيانات؟")
        if result:
            current = self.selected_section.get()

            if current == "المعلومات الشخصية + وصف المشروع":
                # أمثلة المعلومات الشخصية
                if hasattr(self, 'personal_entries'):
                    self.personal_entries["full_name"].delete(0, tk.END)
                    self.personal_entries["full_name"].insert(0, "أحمد محمد علي")
                    self.personal_entries["age"].delete(0, tk.END)
                    self.personal_entries["age"].insert(0, "30")
                    self.personal_entries["education"].delete(0, tk.END)
                    self.personal_entries["education"].insert(0, "بكالوريوس إدارة أعمال")
                    self.personal_entries["experience"].delete(0, tk.END)
                    self.personal_entries["experience"].insert(0, "5 سنوات في مجال التسويق")
                    self.personal_entries["phone"].delete(0, tk.END)
                    self.personal_entries["phone"].insert(0, "0501234567")
                    self.personal_entries["email"].delete(0, tk.END)
                    self.personal_entries["email"].insert(0, "<EMAIL>")

                    self.project_name_entry.delete(0, tk.END)
                    self.project_name_entry.insert(0, "مشروع متجر إلكتروني للمنتجات المحلية")
                    self.project_desc_text.delete(1.0, tk.END)
                    self.project_desc_text.insert(1.0, "متجر إلكتروني متخصص في بيع المنتجات المحلية والحرفية بجودة عالية وأسعار تنافسية")
                    self.funding_amount_entry.delete(0, tk.END)
                    self.funding_amount_entry.insert(0, "100000")

            elif current == "دراسة السوق والمنافسين":
                if hasattr(self, 'products_entry'):
                    self.products_entry.delete(0, tk.END)
                    self.products_entry.insert(0, "منتجات حرفية، أطعمة محلية، ملابس تراثية")
                    self.services_entry.delete(0, tk.END)
                    self.services_entry.insert(0, "خدمة التوصيل، خدمة العملاء، ضمان الجودة")
                    self.competitor_count.delete(0, tk.END)
                    self.competitor_count.insert(0, "3")
                    self.comp_products_entry.delete(0, tk.END)
                    self.comp_products_entry.insert(0, "متاجر إلكترونية أخرى، محلات تقليدية")

            elif current == "تحليل SWOT":
                if hasattr(self, 'strengths_text'):
                    self.strengths_text.delete(1.0, tk.END)
                    self.strengths_text.insert(1.0, "• خبرة في التسويق\n• شبكة علاقات واسعة\n• فهم عميق للسوق المحلي\n• منتجات عالية الجودة")
                    self.weaknesses_text.delete(1.0, tk.END)
                    self.weaknesses_text.insert(1.0, "• رأس مال محدود\n• نقص في الخبرة التقنية\n• فريق عمل صغير\n• عدم وجود علامة تجارية معروفة")
                    self.opportunities_text.delete(1.0, tk.END)
                    self.opportunities_text.insert(1.0, "• نمو التجارة الإلكترونية\n• دعم حكومي للمشاريع الصغيرة\n• زيادة الاهتمام بالمنتجات المحلية\n• توفر منصات التسويق الرقمي")
                    self.threats_text.delete(1.0, tk.END)
                    self.threats_text.insert(1.0, "• منافسة شديدة\n• تقلبات اقتصادية\n• تغيير في سلوك المستهلكين\n• مشاكل في سلسلة التوريد")

            elif current == "المزيج التسويقي":
                if hasattr(self, 'product_text'):
                    self.product_text.delete(1.0, tk.END)
                    self.product_text.insert(1.0, "منتجات حرفية أصيلة عالية الجودة مع ضمان الأصالة والجودة")
                    self.price_text.delete(1.0, tk.END)
                    self.price_text.insert(1.0, "أسعار تنافسية مع خصومات للعملاء الدائمين وعروض موسمية")
                    self.place_text.delete(1.0, tk.END)
                    self.place_text.insert(1.0, "متجر إلكتروني، وسائل التواصل الاجتماعي، معارض محلية")
                    self.promotion_text.delete(1.0, tk.END)
                    self.promotion_text.insert(1.0, "التسويق عبر وسائل التواصل الاجتماعي، إعلانات مدفوعة، تسويق بالمحتوى")
                    self.people_text.delete(1.0, tk.END)
                    self.people_text.insert(1.0, "فريق متخصص في خدمة العملاء، موردين موثوقين، عملاء مستهدفين من محبي المنتجات المحلية")

            messagebox.showinfo("تم الإضافة", f"تم إضافة الأمثلة لقسم: {current}")

    def open_project(self):
        """فتح مشروع"""
        filename = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                messagebox.showinfo("نجح", "تم فتح المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        filename = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.data = {
                    'timestamp': datetime.now().isoformat(),
                    'current_section': self.selected_section.get()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        messagebox.showinfo("قريباً", "ميزة تصدير PDF ستكون متاحة قريباً")

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📋 دليل استخدام نظام تخطيط المشاريع الشامل

        🎯 كيفية الاستخدام:
        1. اختر القسم من القائمة الجانبية
        2. املأ البيانات المطلوبة بالتفصيل
        3. استخدم الأدوات السريعة للحفظ والمسح
        4. استخدم الأمثلة للتعلم

        📊 الأقسام المتاحة:
        • المعلومات الشخصية ووصف المشروع
        • دراسة السوق والمنافسين
        • تحليل SWOT الشامل
        • المزيج التسويقي الخماسي
        • مستلزمات الإنتاج والمعدات
        • الدراسات المالية المتكاملة
        • الإيرادات والأرباح السنوية

        💡 نصائح:
        • ابدأ بالمعلومات الشخصية
        • أكمل الأقسام بالترتيب
        • استخدم الأمثلة للفهم
        • احفظ عملك بانتظام
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "نظام تخطيط المشاريع الشامل\nالإصدار 1.0\n\nأداة متكاملة لتخطيط وتحليل المشاريع")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = ComprehensiveProjectPlanner()
    app.run()
