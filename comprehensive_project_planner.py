#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تخطيط المشاريع الشامل
Comprehensive Project Planning System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class ComprehensiveProjectPlanner:
    def __init__(self):
        # إعداد النافذة الرئيسية - تصميم حصري
        self.root = tk.Tk()
        self.root.title("نظام تخطيط المشاريع الشامل - الإصدار الحصري")
        self.root.geometry("1400x900")
        self.root.configure(bg="#f1f5f9")
        self.root.state('zoomed')  # ملء الشاشة

        # إضافة أيقونة مخصصة (اختيارية)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # ألوان وخطوط - تصميم حصري وكلاسيكي
        self.primary_color = "#1a365d"      # أزرق داكن كلاسيكي
        self.secondary_color = "#2d3748"    # رمادي داكن أنيق
        self.accent_color = "#d4af37"       # ذهبي كلاسيكي
        self.header_color = "#0f172a"       # أسود مزرق عميق
        self.box_bg = "#fefefe"             # أبيض كريمي
        self.sidebar_bg = "#f8fafc"         # رمادي فاتح راقي
        self.border_color = "#e2e8f0"       # حدود رقيقة

        # خطوط كلاسيكية أنيقة
        self.font_title = ("Georgia", 20, "bold")
        self.font_subtitle = ("Georgia", 14, "italic")
        self.font_normal = ("Segoe UI", 12)
        self.font_small = ("Segoe UI", 10)
        self.font_button = ("Segoe UI", 11, "bold")
        
        # تخزين البيانات
        self.data = {}
        
        # الأقسام الرسمية حسب خطة العمل
        self.sections = [
            ("1️⃣", "المعلومات الشخصية + وصف المشروع"),
            ("2️⃣", "دراسة السوق والمنافسين"),
            ("3️⃣", "تحليل SWOT"),
            ("4️⃣", "المزيج التسويقي"),
            ("5️⃣", "مستلزمات الإنتاج"),
            ("6️⃣", "الدراسة المالية – التأسيسية والثابتة"),
            ("7️⃣", "الدراسة المالية – التشغيلية"),
            ("8️⃣", "الدراسة المالية – ملخص شامل"),
            ("9️⃣", "الإيرادات السنوية"),
            ("🔟", "الربح والخسارة السنوي"),
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        self.create_menu()
        
        # --- رأس الصفحة الحصري ---
        # إطار الرأس الرئيسي مع تدرج لوني
        header_main = tk.Frame(self.root, bg="#0f172a", height=120)
        header_main.pack(fill="x")
        header_main.pack_propagate(False)

        # إطار داخلي للمحتوى
        header_content = tk.Frame(header_main, bg="#0f172a")
        header_content.pack(expand=True, fill="both")

        # العنوان الرئيسي مع تأثير ذهبي
        main_title = tk.Label(header_content, text="⚜️ نظام تخطيط المشاريع الشامل ⚜️",
                             font=("Georgia", 24, "bold"),
                             bg="#0f172a", fg="#d4af37")
        main_title.pack(pady=(20, 5))

        # العنوان الفرعي الأنيق
        subtitle = tk.Label(header_content, text="الإصدار الحصري • أداة متكاملة لتخطيط وتحليل المشاريع الاحترافية",
                           font=("Georgia", 12, "italic"),
                           bg="#0f172a", fg="#94a3b8")
        subtitle.pack(pady=(0, 10))

        # خط فاصل ذهبي أنيق
        separator_frame = tk.Frame(self.root, bg="#d4af37", height=3)
        separator_frame.pack(fill="x")

        # إطار ثانوي بتدرج رقيق
        sub_header = tk.Frame(self.root, bg="#1e293b", height=40)
        sub_header.pack(fill="x")
        sub_header.pack_propagate(False)

        # معلومات إضافية
        info_label = tk.Label(sub_header, text="🏆 تصميم حصري وكلاسيكي • واجهة احترافية متطورة",
                             font=("Segoe UI", 10),
                             bg="#1e293b", fg="#cbd5e1")
        info_label.pack(expand=True)

        # --- إطار رئيسي مقسم إلى عمودين ---
        main_frame = tk.Frame(self.root, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # منطقة المحتوى (في الجهة اليسرى) مع تصميم حصري
        content_container = tk.Frame(main_frame, bg="#e2e8f0", relief="ridge", bd=2)
        content_container.pack(side="left", fill="both", expand=True, padx=(0, 15))

        self.content = tk.Frame(content_container, bg="#fefefe")
        self.content.pack(fill="both", expand=True, padx=3, pady=3)

        # القائمة الجانبية اليمنى (في الجهة الشرقية) مع تصميم كلاسيكي
        sidebar_container = tk.Frame(main_frame, bg="#1a365d", relief="ridge", bd=3, width=320)
        sidebar_container.pack(side="right", fill="y")
        sidebar_container.pack_propagate(False)

        self.sidebar = tk.Frame(sidebar_container, bg="#f8fafc")
        self.sidebar.pack(fill="both", expand=True, padx=3, pady=3)

        # دالة عرض القسم المختار
        self.selected_section = tk.StringVar(value="المعلومات الشخصية + وصف المشروع")
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # عرض القسم الأول تلقائيًا
        self.show_section("المعلومات الشخصية + وصف المشروع")
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير PDF", command=self.export_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="حساب الإجماليات", command=self.calculate_totals)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية الحصرية"""
        # رأس القائمة الجانبية مع تصميم كلاسيكي فاخر
        header_frame = tk.Frame(self.sidebar, bg="#0f172a", height=80)
        header_frame.pack(fill="x", pady=(0, 20))
        header_frame.pack_propagate(False)

        # العنوان مع تأثير ذهبي
        sidebar_title = tk.Label(header_frame, text="⚜️ أقسام خطة العمل ⚜️",
                                font=("Georgia", 14, "bold"), bg="#0f172a", fg="#d4af37")
        sidebar_title.pack(expand=True)

        # خط ذهبي تحت العنوان
        gold_line = tk.Frame(self.sidebar, bg="#d4af37", height=2)
        gold_line.pack(fill="x", padx=20, pady=(0, 15))

        # أزرار القائمة الجانبية مع تصميم حصري
        for i, (icon, name) in enumerate(self.sections):
            # إطار للزر مع تأثيرات بصرية فاخرة
            btn_container = tk.Frame(self.sidebar, bg="#f8fafc")
            btn_container.pack(fill="x", padx=12, pady=4)

            # إطار داخلي مع حدود أنيقة
            btn_frame = tk.Frame(btn_container, bg="#ffffff", relief="solid", bd=1)
            btn_frame.pack(fill="x", padx=2, pady=2)

            btn = tk.Radiobutton(
                btn_frame, text=f"{name} {icon}", variable=self.selected_section, value=name,
                indicatoron=0, command=lambda n=name: self.show_section(n),
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#1a365d",
                activebackground="#d4af37", activeforeground="#0f172a",
                selectcolor="#1a365d",
                width=28, pady=15, anchor="center",
                relief="flat", bd=0,
                highlightthickness=0,
                cursor="hand2"
            )
            btn.pack(fill="x", padx=3, pady=3)

            # تأثيرات hover متطورة
            def on_enter(event, button=btn, frame=btn_frame):
                if button.cget('bg') != button.cget('selectcolor'):
                    button.config(bg="#f1f5f9")
                    frame.config(bg="#d4af37", relief="raised", bd=2)

            def on_leave(event, button=btn, frame=btn_frame):
                if button.cget('bg') != button.cget('selectcolor'):
                    button.config(bg="#ffffff")
                    frame.config(bg="#ffffff", relief="solid", bd=1)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

        # مساحة فارغة أنيقة
        spacer = tk.Frame(self.sidebar, bg="#f8fafc", height=20)
        spacer.pack(fill="x")

        # خط فاصل ذهبي
        separator = tk.Frame(self.sidebar, bg="#d4af37", height=3)
        separator.pack(fill="x", padx=20, pady=10)

        # أزرار سريعة مع تصميم فاخر
        tools_header = tk.Frame(self.sidebar, bg="#1e293b", height=50)
        tools_header.pack(fill="x", padx=12, pady=(10, 0))
        tools_header.pack_propagate(False)

        tools_title = tk.Label(tools_header, text="⚡ أدوات سريعة",
                              font=("Georgia", 12, "bold"), bg="#1e293b", fg="#d4af37")
        tools_title.pack(expand=True)

        # إطار الأزرار
        buttons_container = tk.Frame(self.sidebar, bg="#f8fafc", relief="groove", bd=2)
        buttons_container.pack(fill="x", padx=12, pady=(0, 15))

        # أزرار بتصميم حصري
        buttons_data = [
            ("💾 حفظ سريع", self.quick_save, "#1a365d", "#d4af37"),
            ("🔄 مسح القسم", self.clear_current_section, "#dc2626", "#ffffff"),
            ("💡 إضافة أمثلة", self.add_examples, "#d97706", "#ffffff"),
            ("📊 حساب الإجماليات", self.calculate_totals, "#7c3aed", "#ffffff")
        ]

        for text, command, bg_color, fg_color in buttons_data:
            btn_wrapper = tk.Frame(buttons_container, bg="#f8fafc")
            btn_wrapper.pack(fill="x", pady=4, padx=8)

            btn = tk.Button(btn_wrapper, text=text, command=command,
                           bg=bg_color, fg=fg_color, font=self.font_button,
                           relief="raised", bd=2, pady=12, cursor="hand2",
                           activebackground=self._darken_color(bg_color),
                           activeforeground=fg_color)
            btn.pack(fill="x")

            # تأثيرات hover للأزرار
            def on_btn_enter(event, button=btn, orig_bg=bg_color, orig_fg=fg_color):
                button.config(bg=self._darken_color(orig_bg), relief="sunken")

            def on_btn_leave(event, button=btn, orig_bg=bg_color, orig_fg=fg_color):
                button.config(bg=orig_bg, relief="raised")

            btn.bind("<Enter>", on_btn_enter)
            btn.bind("<Leave>", on_btn_leave)

    def _darken_color(self, color):
        """تغميق اللون للتأثيرات البصرية - محدث للتصميم الحصري"""
        color_map = {
            "#1a365d": "#0f172a",  # أزرق داكن إلى أسود مزرق
            "#dc2626": "#b91c1c",  # أحمر داكن
            "#d97706": "#c2410c",  # برتقالي داكن
            "#7c3aed": "#6d28d9",  # بنفسجي داكن
            "#27ae60": "#229954",  # أخضر داكن
            "#e74c3c": "#c0392b",  # أحمر كلاسيكي
            "#f39c12": "#e67e22",  # برتقالي كلاسيكي
            "#9b59b6": "#8e44ad"   # بنفسجي كلاسيكي
        }
        return color_map.get(color, color)

    def show_section(self, name):
        """عرض القسم المختار"""
        self.selected_section.set(name)
        for widget in self.content.winfo_children():
            widget.destroy()
            
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(self.content, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self.content, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # عنوان القسم مع تصميم حصري وكلاسيكي
        title_container = tk.Frame(scrollable_frame, bg="#fefefe")
        title_container.pack(fill="x", pady=(15, 25), padx=25)

        # إطار خارجي مع حدود ذهبية
        title_outer = tk.Frame(title_container, bg="#d4af37", relief="raised", bd=3)
        title_outer.pack(fill="x")

        # إطار داخلي مع تدرج لوني
        title_inner = tk.Frame(title_outer, bg="#0f172a", height=100)
        title_inner.pack(fill="x", padx=3, pady=3)
        title_inner.pack_propagate(False)

        # العنوان الرئيسي مع تأثير ذهبي
        section_title = tk.Label(title_inner, text=f"⚜️ {name} ⚜️",
                               font=("Georgia", 18, "bold"),
                               bg="#0f172a", fg="#d4af37")
        section_title.pack(expand=True)

        # خط ذهبي تحت العنوان
        gold_line = tk.Frame(title_container, bg="#d4af37", height=4)
        gold_line.pack(fill="x", pady=(3, 0))

        # خط رقيق أسفل
        thin_line = tk.Frame(title_container, bg="#1a365d", height=2)
        thin_line.pack(fill="x")

        # مساحة فارغة أنيقة
        spacer = tk.Frame(scrollable_frame, bg="#fefefe", height=15)
        spacer.pack(fill="x")
        
        # محتوى القسم حسب النوع
        if name == "المعلومات الشخصية + وصف المشروع":
            self.create_personal_info_content(scrollable_frame)
        elif name == "دراسة السوق والمنافسين":
            self.create_market_analysis_content(scrollable_frame)
        elif name == "تحليل SWOT":
            self.create_swot_analysis_content(scrollable_frame)
        elif name == "المزيج التسويقي":
            self.create_marketing_mix_content(scrollable_frame)
        elif name == "مستلزمات الإنتاج":
            self.create_production_requirements_content(scrollable_frame)
        elif name == "الدراسة المالية – التأسيسية والثابتة":
            self.create_startup_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – التشغيلية":
            self.create_operational_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – ملخص شامل":
            self.create_financial_summary_content(scrollable_frame)
        elif name == "الإيرادات السنوية":
            self.create_annual_revenue_content(scrollable_frame)
        elif name == "الربح والخسارة السنوي":
            self.create_annual_profit_loss_content(scrollable_frame)
        
        scrollbar.pack(side="left", fill="y")
        canvas.pack(side="right", fill="both", expand=True)

    def create_personal_info_content(self, parent):
        """محتوى قسم المعلومات الشخصية - تصميم حصري وبهي"""

        # === القسم الأول: المعلومات الشخصية ===
        personal_section = self._create_elegant_section(parent, "👤 المعلومات الشخصية", "#1a365d")

        # شبكة الحقول الشخصية مع تصميم فاخر
        personal_grid = tk.Frame(personal_section, bg=self.box_bg)
        personal_grid.pack(fill="x", padx=25, pady=20)

        # الحقول الشخصية مع أيقونات وتصميم راقي
        personal_fields = [
            ("👤", "الاسم الكامل", "full_name", "أدخل اسمك الكامل بالعربية أو الإنجليزية"),
            ("🎂", "العمر", "age", "العمر بالسنوات"),
            ("🎓", "المؤهل العلمي", "education", "آخر مؤهل علمي حصلت عليه"),
            ("💼", "الخبرة المهنية", "experience", "وصف موجز لخبرتك في مجال العمل"),
            ("📱", "رقم الهاتف", "phone", "رقم الهاتف مع رمز البلد (+966)"),
            ("📧", "البريد الإلكتروني", "email", "عنوان البريد الإلكتروني الصحيح")
        ]

        self.personal_entries = {}

        for i, (icon, label, key, placeholder) in enumerate(personal_fields):
            # إطار الحقل مع تصميم فاخر
            field_container = tk.Frame(personal_grid, bg="#f8fafc", relief="solid", bd=1)
            field_container.grid(row=i//2, column=i%2, sticky="ew", padx=10, pady=8)
            personal_grid.grid_columnconfigure(0, weight=1)
            personal_grid.grid_columnconfigure(1, weight=1)

            # رأس الحقل مع أيقونة
            field_header = tk.Frame(field_container, bg="#1a365d", height=35)
            field_header.pack(fill="x")
            field_header.pack_propagate(False)

            header_label = tk.Label(field_header, text=f"{icon} {label}",
                                  font=("Georgia", 11, "bold"), bg="#1a365d", fg="#d4af37")
            header_label.pack(expand=True)

            # حقل الإدخال مع تصميم أنيق
            entry_frame = tk.Frame(field_container, bg="#ffffff")
            entry_frame.pack(fill="x", padx=8, pady=8)

            entry = tk.Entry(entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                           bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
            entry.pack(fill="x", ipady=8)

            # خط تحت الحقل
            underline = tk.Frame(entry_frame, bg="#e2e8f0", height=2)
            underline.pack(fill="x", pady=(2, 0))

            # إضافة placeholder مع تأثيرات متطورة
            self._add_placeholder_effect(entry, placeholder, underline)
            self.personal_entries[key] = entry

        # === القسم الثاني: معلومات المشروع ===
        project_section = self._create_elegant_section(parent, "🚀 معلومات المشروع", "#0f172a")

        # اسم المشروع مع تصميم مميز
        project_name_container = tk.Frame(project_section, bg=self.box_bg)
        project_name_container.pack(fill="x", padx=25, pady=15)

        name_frame = self._create_input_field(project_name_container, "🏢", "اسم المشروع",
                                            "أدخل اسم مشروعك التجاري")
        self.project_name_entry = name_frame

        # وصف المشروع مع تصميم راقي
        desc_container = tk.Frame(project_section, bg=self.box_bg)
        desc_container.pack(fill="x", padx=25, pady=15)

        desc_header = tk.Frame(desc_container, bg="#0f172a", height=40)
        desc_header.pack(fill="x")
        desc_header.pack_propagate(False)

        tk.Label(desc_header, text="📝 وصف المشروع التفصيلي",
                font=("Georgia", 12, "bold"), bg="#0f172a", fg="#d4af37").pack(expand=True)

        # منطقة النص مع تصميم فاخر
        text_frame = tk.Frame(desc_container, bg="#ffffff", relief="solid", bd=2)
        text_frame.pack(fill="x", pady=(0, 5))

        self.project_desc_text = tk.Text(text_frame, height=6, font=("Segoe UI", 11),
                                       relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                       wrap=tk.WORD, insertbackground="#1a365d")

        desc_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.project_desc_text.yview)
        self.project_desc_text.configure(yscrollcommand=desc_scrollbar.set)

        self.project_desc_text.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        desc_scrollbar.pack(side="right", fill="y")

        # === القسم الثالث: التمويل ===
        funding_section = self._create_elegant_section(parent, "💰 خطة التمويل", "#1e293b")

        funding_container = tk.Frame(funding_section, bg=self.box_bg)
        funding_container.pack(fill="x", padx=25, pady=20)

        # نوع التمويل مع تصميم راقي
        funding_type_frame = tk.Frame(funding_container, bg="#f8fafc", relief="solid", bd=1)
        funding_type_frame.pack(fill="x", pady=10)

        type_header = tk.Frame(funding_type_frame, bg="#1e293b", height=40)
        type_header.pack(fill="x")
        type_header.pack_propagate(False)

        tk.Label(type_header, text="💳 نوع التمويل المطلوب",
                font=("Georgia", 12, "bold"), bg="#1e293b", fg="#d4af37").pack(expand=True)

        # خيارات التمويل مع تصميم أنيق
        options_container = tk.Frame(funding_type_frame, bg="#ffffff")
        options_container.pack(fill="x", padx=15, pady=15)

        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = [
            ("💼", "تمويل ذاتي", "ذاتي"),
            ("🏦", "قرض بنكي", "قرض بنكي"),
            ("👥", "مستثمر خارجي", "مستثمر"),
            ("🔄", "تمويل مختلط", "مختلط")
        ]

        for i, (icon, text, value) in enumerate(funding_options):
            option_frame = tk.Frame(options_container, bg="#f8fafc", relief="solid", bd=1)
            option_frame.grid(row=0, column=i, padx=5, pady=5, sticky="ew")
            options_container.grid_columnconfigure(i, weight=1)

            rb = tk.Radiobutton(option_frame, text=f"{icon}\n{text}", variable=self.funding_type,
                              value=value, bg="#f8fafc", font=("Segoe UI", 10, "bold"),
                              fg="#1a365d", selectcolor="#d4af37", indicatoron=0,
                              activebackground="#e2e8f0", cursor="hand2", pady=10)
            rb.pack(fill="both", expand=True)

        # مبلغ التمويل مع تصميم مميز
        amount_frame = self._create_input_field(funding_container, "💵", "مبلغ التمويل المطلوب (ريال سعودي)",
                                              "أدخل المبلغ بالأرقام فقط")
        self.funding_amount_entry = amount_frame

        # === أزرار العمليات الحصرية ===
        self._create_action_buttons(parent)

    def _create_elegant_section(self, parent, title, bg_color):
        """إنشاء قسم أنيق مع تصميم حصري"""
        # إطار القسم الرئيسي
        section_container = tk.Frame(parent, bg=self.box_bg)
        section_container.pack(fill="x", padx=20, pady=15)

        # إطار خارجي مع حدود ذهبية
        outer_frame = tk.Frame(section_container, bg="#d4af37", relief="raised", bd=3)
        outer_frame.pack(fill="x")

        # رأس القسم مع تدرج لوني
        header_frame = tk.Frame(outer_frame, bg=bg_color, height=50)
        header_frame.pack(fill="x", padx=3, pady=(3, 0))
        header_frame.pack_propagate(False)

        # العنوان مع تأثير ذهبي
        title_label = tk.Label(header_frame, text=title, font=("Georgia", 14, "bold"),
                              bg=bg_color, fg="#d4af37")
        title_label.pack(expand=True)

        # المحتوى الداخلي
        content_frame = tk.Frame(outer_frame, bg=self.box_bg)
        content_frame.pack(fill="x", padx=3, pady=(0, 3))

        return content_frame

    def _create_input_field(self, parent, icon, label, placeholder):
        """إنشاء حقل إدخال أنيق"""
        # إطار الحقل
        field_container = tk.Frame(parent, bg="#f8fafc", relief="solid", bd=1)
        field_container.pack(fill="x", pady=8)

        # رأس الحقل
        field_header = tk.Frame(field_container, bg="#1a365d", height=35)
        field_header.pack(fill="x")
        field_header.pack_propagate(False)

        header_label = tk.Label(field_header, text=f"{icon} {label}",
                              font=("Georgia", 11, "bold"), bg="#1a365d", fg="#d4af37")
        header_label.pack(expand=True)

        # حقل الإدخال
        entry_frame = tk.Frame(field_container, bg="#ffffff")
        entry_frame.pack(fill="x", padx=8, pady=8)

        entry = tk.Entry(entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                        bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
        entry.pack(fill="x", ipady=8)

        # خط تحت الحقل
        underline = tk.Frame(entry_frame, bg="#e2e8f0", height=2)
        underline.pack(fill="x", pady=(2, 0))

        # إضافة placeholder
        self._add_placeholder_effect(entry, placeholder, underline)

        return entry

    def _add_placeholder_effect(self, entry, placeholder, underline=None):
        """إضافة تأثير placeholder متطور"""
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")

        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg="#2c3e50")
            if underline:
                underline.config(bg="#d4af37")

        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8")
            if underline:
                underline.config(bg="#e2e8f0")

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    def _create_action_buttons(self, parent):
        """إنشاء أزرار العمليات بتصميم حصري"""
        # إطار الأزرار
        buttons_container = tk.Frame(parent, bg=self.box_bg)
        buttons_container.pack(fill="x", padx=20, pady=25)

        # خط ذهبي فاصل
        separator = tk.Frame(buttons_container, bg="#d4af37", height=3)
        separator.pack(fill="x", pady=(0, 20))

        # إطار الأزرار مع تصميم فاخر
        buttons_frame = tk.Frame(buttons_container, bg="#f8fafc", relief="groove", bd=2)
        buttons_frame.pack(fill="x")

        # رأس الأزرار
        buttons_header = tk.Frame(buttons_frame, bg="#1a365d", height=40)
        buttons_header.pack(fill="x")
        buttons_header.pack_propagate(False)

        tk.Label(buttons_header, text="⚡ عمليات سريعة", font=("Georgia", 12, "bold"),
                bg="#1a365d", fg="#d4af37").pack(expand=True)

        # الأزرار
        buttons_inner = tk.Frame(buttons_frame, bg="#ffffff")
        buttons_inner.pack(fill="x", padx=15, pady=15)

        buttons_data = [
            ("💾", "حفظ البيانات", self.quick_save, "#27ae60"),
            ("🔄", "مسح البيانات", self.clear_current_section, "#e74c3c"),
            ("💡", "إضافة مثال", self.add_examples, "#f39c12"),
            ("📊", "معاينة البيانات", self.preview_data, "#3498db")
        ]

        for i, (icon, text, command, color) in enumerate(buttons_data):
            btn_frame = tk.Frame(buttons_inner, bg="#f8fafc", relief="solid", bd=1)
            btn_frame.grid(row=0, column=i, padx=8, pady=5, sticky="ew")
            buttons_inner.grid_columnconfigure(i, weight=1)

            btn = tk.Button(btn_frame, text=f"{icon}\n{text}", command=command,
                           bg=color, fg="white", font=("Segoe UI", 10, "bold"),
                           relief="raised", bd=2, pady=15, cursor="hand2",
                           activebackground=self._darken_color(color))
            btn.pack(fill="both", expand=True, padx=3, pady=3)

    def preview_data(self):
        """معاينة البيانات المدخلة"""
        messagebox.showinfo("معاينة البيانات", "ميزة معاينة البيانات ستكون متاحة قريباً")

    def create_market_analysis_content(self, parent):
        """محتوى قسم دراسة السوق - تصميم حصري وبهي"""

        # === القسم الأول: المنتجات والخدمات ===
        products_section = self._create_elegant_section(parent, "🛍️ المنتجات والخدمات", "#1a365d")

        # شبكة المنتجات والخدمات
        products_grid = tk.Frame(products_section, bg=self.box_bg)
        products_grid.pack(fill="x", padx=25, pady=20)

        # المنتجات
        products_container = tk.Frame(products_grid, bg="#f8fafc", relief="solid", bd=1)
        products_container.pack(fill="x", pady=8)

        products_header = tk.Frame(products_container, bg="#1a365d", height=40)
        products_header.pack(fill="x")
        products_header.pack_propagate(False)

        tk.Label(products_header, text="🛍️ المنتجات المقدمة",
                font=("Georgia", 12, "bold"), bg="#1a365d", fg="#d4af37").pack(expand=True)

        products_entry_frame = tk.Frame(products_container, bg="#ffffff")
        products_entry_frame.pack(fill="x", padx=10, pady=10)

        self.products_entry = tk.Entry(products_entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                                     bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
        self.products_entry.pack(fill="x", ipady=8)

        products_underline = tk.Frame(products_entry_frame, bg="#e2e8f0", height=2)
        products_underline.pack(fill="x", pady=(2, 0))

        self._add_placeholder_effect(self.products_entry, "أدخل المنتجات التي سيقدمها مشروعك (مفصولة بفواصل)", products_underline)

        # الخدمات
        services_container = tk.Frame(products_grid, bg="#f8fafc", relief="solid", bd=1)
        services_container.pack(fill="x", pady=8)

        services_header = tk.Frame(services_container, bg="#0f172a", height=40)
        services_header.pack(fill="x")
        services_header.pack_propagate(False)

        tk.Label(services_header, text="🔧 الخدمات المقدمة",
                font=("Georgia", 12, "bold"), bg="#0f172a", fg="#d4af37").pack(expand=True)

        services_entry_frame = tk.Frame(services_container, bg="#ffffff")
        services_entry_frame.pack(fill="x", padx=10, pady=10)

        self.services_entry = tk.Entry(services_entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                                     bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
        self.services_entry.pack(fill="x", ipady=8)

        services_underline = tk.Frame(services_entry_frame, bg="#e2e8f0", height=2)
        services_underline.pack(fill="x", pady=(2, 0))

        self._add_placeholder_effect(self.services_entry, "أدخل الخدمات التي سيقدمها مشروعك (مفصولة بفواصل)", services_underline)

        # === القسم الثاني: تحليل المنافسين ===
        competitors_section = self._create_elegant_section(parent, "⚔️ تحليل المنافسين", "#1e293b")

        competitors_container = tk.Frame(competitors_section, bg=self.box_bg)
        competitors_container.pack(fill="x", padx=25, pady=20)

        # وجود المنافسين
        existence_frame = tk.Frame(competitors_container, bg="#f8fafc", relief="solid", bd=1)
        existence_frame.pack(fill="x", pady=10)

        existence_header = tk.Frame(existence_frame, bg="#1e293b", height=40)
        existence_header.pack(fill="x")
        existence_header.pack_propagate(False)

        tk.Label(existence_header, text="❓ هل يوجد منافسون في السوق؟",
                font=("Georgia", 12, "bold"), bg="#1e293b", fg="#d4af37").pack(expand=True)

        # خيارات وجود المنافسين
        existence_options = tk.Frame(existence_frame, bg="#ffffff")
        existence_options.pack(fill="x", padx=15, pady=15)

        self.has_competitors = tk.StringVar(value="نعم")

        yes_frame = tk.Frame(existence_options, bg="#e8f5e8", relief="solid", bd=1)
        yes_frame.pack(side="right", padx=10, pady=5)

        tk.Radiobutton(yes_frame, text="✅ نعم، يوجد منافسون", variable=self.has_competitors,
                      value="نعم", bg="#e8f5e8", font=("Segoe UI", 11, "bold"),
                      fg="#27ae60", selectcolor="#d4af37", cursor="hand2", pady=10).pack(padx=15)

        no_frame = tk.Frame(existence_options, bg="#ffe8e8", relief="solid", bd=1)
        no_frame.pack(side="right", padx=10, pady=5)

        tk.Radiobutton(no_frame, text="❌ لا، لا يوجد منافسون", variable=self.has_competitors,
                      value="لا", bg="#ffe8e8", font=("Segoe UI", 11, "bold"),
                      fg="#e74c3c", selectcolor="#d4af37", cursor="hand2", pady=10).pack(padx=15)

        # عدد المنافسين
        count_frame = self._create_input_field(competitors_container, "🔢", "عدد المنافسين الرئيسيين",
                                             "أدخل عدد المنافسين المباشرين")
        self.competitor_count = count_frame

        # المنتجات المنافسة
        comp_products_container = tk.Frame(competitors_container, bg="#f8fafc", relief="solid", bd=1)
        comp_products_container.pack(fill="x", pady=10)

        comp_header = tk.Frame(comp_products_container, bg="#1e293b", height=40)
        comp_header.pack(fill="x")
        comp_header.pack_propagate(False)

        tk.Label(comp_header, text="🏪 المنتجات والخدمات المنافسة",
                font=("Georgia", 12, "bold"), bg="#1e293b", fg="#d4af37").pack(expand=True)

        comp_entry_frame = tk.Frame(comp_products_container, bg="#ffffff")
        comp_entry_frame.pack(fill="x", padx=10, pady=10)

        self.comp_products_entry = tk.Entry(comp_entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                                          bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
        self.comp_products_entry.pack(fill="x", ipady=8)

        comp_underline = tk.Frame(comp_entry_frame, bg="#e2e8f0", height=2)
        comp_underline.pack(fill="x", pady=(2, 0))

        self._add_placeholder_effect(self.comp_products_entry, "اذكر المنتجات والخدمات التي يقدمها المنافسون", comp_underline)

        # === أزرار العمليات ===
        self._create_action_buttons(parent)

    def create_swot_analysis_content(self, parent):
        """محتوى قسم تحليل SWOT - تصميم حصري وبهي"""

        # عنوان تعريفي للتحليل
        intro_section = self._create_elegant_section(parent, "⚖️ تحليل SWOT الاستراتيجي", "#0f172a")

        intro_text = tk.Label(intro_section,
                             text="تحليل نقاط القوة والضعف والفرص والتهديدات لمشروعك",
                             font=("Georgia", 12, "italic"), bg=self.box_bg, fg="#64748b")
        intro_text.pack(pady=10)

        # === الشبكة الرئيسية للتحليل ===
        main_container = tk.Frame(parent, bg=self.box_bg)
        main_container.pack(fill="both", expand=True, padx=20, pady=15)

        # إطار خارجي مع حدود ذهبية
        swot_outer = tk.Frame(main_container, bg="#d4af37", relief="raised", bd=3)
        swot_outer.pack(fill="both", expand=True)

        # الشبكة الداخلية
        swot_grid = tk.Frame(swot_outer, bg=self.box_bg)
        swot_grid.pack(fill="both", expand=True, padx=3, pady=3)

        # === نقاط القوة (أعلى يسار) ===
        strengths_container = tk.Frame(swot_grid, bg="#e8f5e8", relief="solid", bd=2)
        strengths_container.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        # رأس نقاط القوة
        strengths_header = tk.Frame(strengths_container, bg="#27ae60", height=50)
        strengths_header.pack(fill="x")
        strengths_header.pack_propagate(False)

        tk.Label(strengths_header, text="💪 نقاط القوة (Strengths)",
                font=("Georgia", 14, "bold"), bg="#27ae60", fg="white").pack(expand=True)

        # منطقة النص
        strengths_text_frame = tk.Frame(strengths_container, bg="#ffffff")
        strengths_text_frame.pack(fill="both", expand=True, padx=8, pady=8)

        self.strengths_text = tk.Text(strengths_text_frame, font=("Segoe UI", 11),
                                    relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                    wrap=tk.WORD, insertbackground="#27ae60")

        strengths_scroll = ttk.Scrollbar(strengths_text_frame, orient="vertical", command=self.strengths_text.yview)
        self.strengths_text.configure(yscrollcommand=strengths_scroll.set)

        self.strengths_text.pack(side="left", fill="both", expand=True)
        strengths_scroll.pack(side="right", fill="y")

        # === نقاط الضعف (أعلى يمين) ===
        weaknesses_container = tk.Frame(swot_grid, bg="#ffe8e8", relief="solid", bd=2)
        weaknesses_container.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        # رأس نقاط الضعف
        weaknesses_header = tk.Frame(weaknesses_container, bg="#e74c3c", height=50)
        weaknesses_header.pack(fill="x")
        weaknesses_header.pack_propagate(False)

        tk.Label(weaknesses_header, text="⚠️ نقاط الضعف (Weaknesses)",
                font=("Georgia", 14, "bold"), bg="#e74c3c", fg="white").pack(expand=True)

        # منطقة النص
        weaknesses_text_frame = tk.Frame(weaknesses_container, bg="#ffffff")
        weaknesses_text_frame.pack(fill="both", expand=True, padx=8, pady=8)

        self.weaknesses_text = tk.Text(weaknesses_text_frame, font=("Segoe UI", 11),
                                     relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                     wrap=tk.WORD, insertbackground="#e74c3c")

        weaknesses_scroll = ttk.Scrollbar(weaknesses_text_frame, orient="vertical", command=self.weaknesses_text.yview)
        self.weaknesses_text.configure(yscrollcommand=weaknesses_scroll.set)

        self.weaknesses_text.pack(side="left", fill="both", expand=True)
        weaknesses_scroll.pack(side="right", fill="y")

        # === الفرص (أسفل يسار) ===
        opportunities_container = tk.Frame(swot_grid, bg="#e8f0ff", relief="solid", bd=2)
        opportunities_container.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

        # رأس الفرص
        opportunities_header = tk.Frame(opportunities_container, bg="#3498db", height=50)
        opportunities_header.pack(fill="x")
        opportunities_header.pack_propagate(False)

        tk.Label(opportunities_header, text="🌟 الفرص (Opportunities)",
                font=("Georgia", 14, "bold"), bg="#3498db", fg="white").pack(expand=True)

        # منطقة النص
        opportunities_text_frame = tk.Frame(opportunities_container, bg="#ffffff")
        opportunities_text_frame.pack(fill="both", expand=True, padx=8, pady=8)

        self.opportunities_text = tk.Text(opportunities_text_frame, font=("Segoe UI", 11),
                                        relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                        wrap=tk.WORD, insertbackground="#3498db")

        opportunities_scroll = ttk.Scrollbar(opportunities_text_frame, orient="vertical", command=self.opportunities_text.yview)
        self.opportunities_text.configure(yscrollcommand=opportunities_scroll.set)

        self.opportunities_text.pack(side="left", fill="both", expand=True)
        opportunities_scroll.pack(side="right", fill="y")

        # === التهديدات (أسفل يمين) ===
        threats_container = tk.Frame(swot_grid, bg="#fff0e8", relief="solid", bd=2)
        threats_container.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

        # رأس التهديدات
        threats_header = tk.Frame(threats_container, bg="#f39c12", height=50)
        threats_header.pack(fill="x")
        threats_header.pack_propagate(False)

        tk.Label(threats_header, text="⚡ التهديدات (Threats)",
                font=("Georgia", 14, "bold"), bg="#f39c12", fg="white").pack(expand=True)

        # منطقة النص
        threats_text_frame = tk.Frame(threats_container, bg="#ffffff")
        threats_text_frame.pack(fill="both", expand=True, padx=8, pady=8)

        self.threats_text = tk.Text(threats_text_frame, font=("Segoe UI", 11),
                                  relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                  wrap=tk.WORD, insertbackground="#f39c12")

        threats_scroll = ttk.Scrollbar(threats_text_frame, orient="vertical", command=self.threats_text.yview)
        self.threats_text.configure(yscrollcommand=threats_scroll.set)

        self.threats_text.pack(side="left", fill="both", expand=True)
        threats_scroll.pack(side="right", fill="y")

        # تكوين الشبكة للتوسع المتساوي
        swot_grid.grid_rowconfigure(0, weight=1)
        swot_grid.grid_rowconfigure(1, weight=1)
        swot_grid.grid_columnconfigure(0, weight=1)
        swot_grid.grid_columnconfigure(1, weight=1)

        # === أزرار العمليات ===
        self._create_action_buttons(parent)

    def create_marketing_mix_content(self, parent):
        """محتوى قسم المزيج التسويقي"""
        # المنتج
        product_frame = tk.LabelFrame(parent, text="🛍️ المنتج",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        product_frame.pack(fill="x", padx=20, pady=10)

        self.product_text = tk.Text(product_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.product_text.pack(fill="x", padx=10, pady=10)

        # السعر
        price_frame = tk.LabelFrame(parent, text="💰 السعر",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=20, pady=10)

        self.price_text = tk.Text(price_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.price_text.pack(fill="x", padx=10, pady=10)

        # المكان
        place_frame = tk.LabelFrame(parent, text="📍 المكان",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        place_frame.pack(fill="x", padx=20, pady=10)

        self.place_text = tk.Text(place_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.place_text.pack(fill="x", padx=10, pady=10)

        # الترويج
        promotion_frame = tk.LabelFrame(parent, text="📣 الترويج",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=20, pady=10)

        self.promotion_text = tk.Text(promotion_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.promotion_text.pack(fill="x", padx=10, pady=10)

        # الناس
        people_frame = tk.LabelFrame(parent, text="👥 الناس",
                                   font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        people_frame.pack(fill="x", padx=20, pady=10)

        self.people_text = tk.Text(people_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.people_text.pack(fill="x", padx=10, pady=10)

    def create_production_requirements_content(self, parent):
        """محتوى قسم مستلزمات الإنتاج"""
        # المعدات والآلات
        equipment_frame = tk.LabelFrame(parent, text="🔧 المعدات والآلات",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        equipment_frame.pack(fill="x", padx=20, pady=10)

        # جدول المعدات
        equipment_table_frame = tk.Frame(equipment_frame, bg=self.box_bg)
        equipment_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        headers = ["المعدة/الآلة", "الكمية", "السعر الواحد", "الإجمالي"]
        for i, header in enumerate(headers):
            tk.Label(equipment_table_frame, text=header, font=self.font_normal,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المعدات
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف للمعدات
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(equipment_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(equipment_table_frame, width=15, font=self.font_small)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_equipment_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.equipment_entries.append(row_entries)

        # إجمالي المعدات
        tk.Label(equipment_table_frame, text="إجمالي المعدات:", font=self.font_normal,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_equipment_label = tk.Label(equipment_table_frame, text="0", font=self.font_normal,
                                            bg="yellow", relief="sunken")
        self.total_equipment_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

    def create_startup_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التأسيسية"""
        costs_frame = tk.LabelFrame(parent, text="التكاليف التأسيسية والثابتة",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        costs_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.startup_costs_text = tk.Text(costs_frame, font=self.font_small, wrap=tk.WORD)
        self.startup_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_operational_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التشغيلية"""
        operational_frame = tk.LabelFrame(parent, text="التكاليف التشغيلية الشهرية",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        operational_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.operational_costs_text = tk.Text(operational_frame, font=self.font_small, wrap=tk.WORD)
        self.operational_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_financial_summary_content(self, parent):
        """محتوى قسم الدراسة المالية الملخص الشامل"""
        summary_frame = tk.LabelFrame(parent, text="الملخص المالي الشامل",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        summary_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.financial_summary_text = tk.Text(summary_frame, font=self.font_small, wrap=tk.WORD)
        self.financial_summary_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_revenue_content(self, parent):
        """محتوى قسم الإيرادات السنوية"""
        revenue_frame = tk.LabelFrame(parent, text="الإيرادات السنوية المتوقعة",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        revenue_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.annual_revenue_text = tk.Text(revenue_frame, font=self.font_small, wrap=tk.WORD)
        self.annual_revenue_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_profit_loss_content(self, parent):
        """محتوى قسم الربح والخسارة السنوي"""
        profit_loss_frame = tk.LabelFrame(parent, text="تحليل الربح والخسارة السنوي",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        profit_loss_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.profit_loss_text = tk.Text(profit_loss_frame, font=self.font_small, wrap=tk.WORD)
        self.profit_loss_text.pack(fill="both", expand=True, padx=10, pady=10)

    # الوظائف المساعدة
    def calculate_equipment_total(self, row):
        """حساب إجمالي المعدة في الصف المحدد"""
        try:
            quantity = float(self.equipment_entries[row][1].get() or 0)
            price = float(self.equipment_entries[row][2].get() or 0)
            total = quantity * price
            self.equipment_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except (ValueError, IndexError):
            pass

    def calculate_production_totals(self):
        """حساب الإجماليات الكلية"""
        equipment_total = 0
        for row in self.equipment_entries:
            try:
                equipment_total += float(row[3].cget("text"))
            except (ValueError, AttributeError):
                pass
        self.total_equipment_label.config(text=f"{equipment_total:.2f}")

    def calculate_totals(self):
        """حساب جميع الإجماليات"""
        self.calculate_production_totals()
        messagebox.showinfo("تم الحساب", "تم حساب جميع الإجماليات بنجاح")

    def quick_save(self):
        """حفظ سريع"""
        messagebox.showinfo("حفظ سريع", "تم الحفظ السريع بنجاح")

    def clear_current_section(self):
        """مسح القسم الحالي"""
        current = self.selected_section.get()
        result = messagebox.askyesno("تأكيد المسح", f"هل تريد مسح بيانات قسم:\n{current}؟")
        if result:
            # إعادة عرض القسم لمسح البيانات
            self.show_section(current)
            messagebox.showinfo("تم المسح", f"تم مسح بيانات قسم {current}")

    def new_project(self):
        """مشروع جديد"""
        result = messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.")
        if result:
            self.clear_all_data()

    def clear_all_data(self):
        """مسح جميع البيانات"""
        # إعادة عرض القسم الأول
        self.show_section("المعلومات الشخصية + وصف المشروع")
        messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def add_examples(self):
        """إضافة أمثلة للبيانات"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة للبيانات؟")
        if result:
            current = self.selected_section.get()

            if current == "المعلومات الشخصية + وصف المشروع":
                # أمثلة المعلومات الشخصية
                if hasattr(self, 'personal_entries'):
                    self.personal_entries["full_name"].delete(0, tk.END)
                    self.personal_entries["full_name"].insert(0, "أحمد محمد علي")
                    self.personal_entries["age"].delete(0, tk.END)
                    self.personal_entries["age"].insert(0, "30")
                    self.personal_entries["education"].delete(0, tk.END)
                    self.personal_entries["education"].insert(0, "بكالوريوس إدارة أعمال")
                    self.personal_entries["experience"].delete(0, tk.END)
                    self.personal_entries["experience"].insert(0, "5 سنوات في مجال التسويق")
                    self.personal_entries["phone"].delete(0, tk.END)
                    self.personal_entries["phone"].insert(0, "0501234567")
                    self.personal_entries["email"].delete(0, tk.END)
                    self.personal_entries["email"].insert(0, "<EMAIL>")

                    self.project_name_entry.delete(0, tk.END)
                    self.project_name_entry.insert(0, "مشروع متجر إلكتروني للمنتجات المحلية")
                    self.project_desc_text.delete(1.0, tk.END)
                    self.project_desc_text.insert(1.0, "متجر إلكتروني متخصص في بيع المنتجات المحلية والحرفية بجودة عالية وأسعار تنافسية")
                    self.funding_amount_entry.delete(0, tk.END)
                    self.funding_amount_entry.insert(0, "100000")

            elif current == "دراسة السوق والمنافسين":
                if hasattr(self, 'products_entry'):
                    self.products_entry.delete(0, tk.END)
                    self.products_entry.insert(0, "منتجات حرفية، أطعمة محلية، ملابس تراثية")
                    self.services_entry.delete(0, tk.END)
                    self.services_entry.insert(0, "خدمة التوصيل، خدمة العملاء، ضمان الجودة")
                    self.competitor_count.delete(0, tk.END)
                    self.competitor_count.insert(0, "3")
                    self.comp_products_entry.delete(0, tk.END)
                    self.comp_products_entry.insert(0, "متاجر إلكترونية أخرى، محلات تقليدية")

            elif current == "تحليل SWOT":
                if hasattr(self, 'strengths_text'):
                    self.strengths_text.delete(1.0, tk.END)
                    self.strengths_text.insert(1.0, "• خبرة في التسويق\n• شبكة علاقات واسعة\n• فهم عميق للسوق المحلي\n• منتجات عالية الجودة")
                    self.weaknesses_text.delete(1.0, tk.END)
                    self.weaknesses_text.insert(1.0, "• رأس مال محدود\n• نقص في الخبرة التقنية\n• فريق عمل صغير\n• عدم وجود علامة تجارية معروفة")
                    self.opportunities_text.delete(1.0, tk.END)
                    self.opportunities_text.insert(1.0, "• نمو التجارة الإلكترونية\n• دعم حكومي للمشاريع الصغيرة\n• زيادة الاهتمام بالمنتجات المحلية\n• توفر منصات التسويق الرقمي")
                    self.threats_text.delete(1.0, tk.END)
                    self.threats_text.insert(1.0, "• منافسة شديدة\n• تقلبات اقتصادية\n• تغيير في سلوك المستهلكين\n• مشاكل في سلسلة التوريد")

            elif current == "المزيج التسويقي":
                if hasattr(self, 'product_text'):
                    self.product_text.delete(1.0, tk.END)
                    self.product_text.insert(1.0, "منتجات حرفية أصيلة عالية الجودة مع ضمان الأصالة والجودة")
                    self.price_text.delete(1.0, tk.END)
                    self.price_text.insert(1.0, "أسعار تنافسية مع خصومات للعملاء الدائمين وعروض موسمية")
                    self.place_text.delete(1.0, tk.END)
                    self.place_text.insert(1.0, "متجر إلكتروني، وسائل التواصل الاجتماعي، معارض محلية")
                    self.promotion_text.delete(1.0, tk.END)
                    self.promotion_text.insert(1.0, "التسويق عبر وسائل التواصل الاجتماعي، إعلانات مدفوعة، تسويق بالمحتوى")
                    self.people_text.delete(1.0, tk.END)
                    self.people_text.insert(1.0, "فريق متخصص في خدمة العملاء، موردين موثوقين، عملاء مستهدفين من محبي المنتجات المحلية")

            messagebox.showinfo("تم الإضافة", f"تم إضافة الأمثلة لقسم: {current}")

    def open_project(self):
        """فتح مشروع"""
        filename = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                messagebox.showinfo("نجح", "تم فتح المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        filename = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.data = {
                    'timestamp': datetime.now().isoformat(),
                    'current_section': self.selected_section.get()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        messagebox.showinfo("قريباً", "ميزة تصدير PDF ستكون متاحة قريباً")

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📋 دليل استخدام نظام تخطيط المشاريع الشامل

        🎯 كيفية الاستخدام:
        1. اختر القسم من القائمة الجانبية
        2. املأ البيانات المطلوبة بالتفصيل
        3. استخدم الأدوات السريعة للحفظ والمسح
        4. استخدم الأمثلة للتعلم

        📊 الأقسام المتاحة:
        • المعلومات الشخصية ووصف المشروع
        • دراسة السوق والمنافسين
        • تحليل SWOT الشامل
        • المزيج التسويقي الخماسي
        • مستلزمات الإنتاج والمعدات
        • الدراسات المالية المتكاملة
        • الإيرادات والأرباح السنوية

        💡 نصائح:
        • ابدأ بالمعلومات الشخصية
        • أكمل الأقسام بالترتيب
        • استخدم الأمثلة للفهم
        • احفظ عملك بانتظام
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "نظام تخطيط المشاريع الشامل\nالإصدار 1.0\n\nأداة متكاملة لتخطيط وتحليل المشاريع")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = ComprehensiveProjectPlanner()
    app.run()
