#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تخطيط المشاريع الشامل
Comprehensive Project Planning System
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class ComprehensiveProjectPlanner:
    def __init__(self):
        # إعداد النافذة الرئيسية - تصميم حصري
        self.root = tk.Tk()
        self.root.title("نظام تخطيط المشاريع الشامل - الإصدار الحصري")
        self.root.geometry("1400x900")
        self.root.configure(bg="#f1f5f9")
        self.root.state('zoomed')  # ملء الشاشة

        # إضافة أيقونة مخصصة (اختيارية)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # ألوان هادئة ومتناسقة لراحة العين
        self.primary_color = "#2c5282"      # أزرق هادئ
        self.secondary_color = "#4a5568"    # رمادي متوسط
        self.accent_color = "#d69e2e"       # ذهبي هادئ
        self.header_color = "#1a202c"       # أسود رمادي
        self.box_bg = "#f7fafc"             # أبيض مزرق فاتح
        self.sidebar_bg = "#edf2f7"         # رمادي فاتح جداً
        self.border_color = "#e2e8f0"       # حدود رقيقة
        self.text_color = "#2d3748"         # نص رمادي داكن
        self.light_bg = "#f1f5f9"           # خلفية فاتحة

        # خطوط تدعم العربية بشكل ممتاز
        self.font_title = ("Tahoma", 18, "bold")
        self.font_subtitle = ("Tahoma", 14, "normal")
        self.font_normal = ("Tahoma", 12, "normal")
        self.font_small = ("Tahoma", 10, "normal")
        self.font_button = ("Tahoma", 11, "bold")
        self.font_arabic = ("Arial", 12, "normal")  # خط احتياطي للعربية
        
        # تخزين البيانات
        self.data = {}
        
        # الأقسام الرسمية حسب خطة العمل
        self.sections = [
            ("1️⃣", "المعلومات الشخصية + وصف المشروع"),
            ("2️⃣", "دراسة السوق والمنافسين"),
            ("3️⃣", "تحليل SWOT"),
            ("4️⃣", "المزيج التسويقي"),
            ("5️⃣", "مستلزمات الإنتاج"),
            ("6️⃣", "الدراسة المالية – التأسيسية والثابتة"),
            ("7️⃣", "الدراسة المالية – التشغيلية"),
            ("8️⃣", "الدراسة المالية – ملخص شامل"),
            ("9️⃣", "الإيرادات السنوية"),
            ("🔟", "الربح والخسارة السنوي"),
        ]
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        self.create_menu()
        
        # --- رأس الصفحة الحصري ---
        # إطار الرأس الرئيسي مع تدرج لوني
        header_main = tk.Frame(self.root, bg="#0f172a", height=120)
        header_main.pack(fill="x")
        header_main.pack_propagate(False)

        # إطار داخلي للمحتوى
        header_content = tk.Frame(header_main, bg="#0f172a")
        header_content.pack(expand=True, fill="both")

        # العنوان الرئيسي مع تأثير ذهبي
        main_title = tk.Label(header_content, text="⚜️ نظام تخطيط المشاريع الشامل ⚜️",
                             font=("Georgia", 24, "bold"),
                             bg="#0f172a", fg="#d4af37")
        main_title.pack(pady=(20, 5))

        # العنوان الفرعي الأنيق
        subtitle = tk.Label(header_content, text="الإصدار الحصري • أداة متكاملة لتخطيط وتحليل المشاريع الاحترافية",
                           font=("Georgia", 12, "italic"),
                           bg="#0f172a", fg="#94a3b8")
        subtitle.pack(pady=(0, 10))

        # خط فاصل ذهبي أنيق
        separator_frame = tk.Frame(self.root, bg="#d4af37", height=3)
        separator_frame.pack(fill="x")

        # إطار ثانوي بتدرج رقيق
        sub_header = tk.Frame(self.root, bg="#1e293b", height=40)
        sub_header.pack(fill="x")
        sub_header.pack_propagate(False)

        # معلومات إضافية
        info_label = tk.Label(sub_header, text="🏆 تصميم حصري وكلاسيكي • واجهة احترافية متطورة",
                             font=("Segoe UI", 10),
                             bg="#1e293b", fg="#cbd5e1")
        info_label.pack(expand=True)

        # --- إطار رئيسي مقسم إلى عمودين ---
        main_frame = tk.Frame(self.root, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # منطقة المحتوى (في الجهة اليسرى) مع تصميم حصري
        content_container = tk.Frame(main_frame, bg="#e2e8f0", relief="ridge", bd=2)
        content_container.pack(side="left", fill="both", expand=True, padx=(0, 15))

        self.content = tk.Frame(content_container, bg="#fefefe")
        self.content.pack(fill="both", expand=True, padx=3, pady=3)

        # القائمة الجانبية اليمنى (في الجهة الشرقية) مع تصميم كلاسيكي
        sidebar_container = tk.Frame(main_frame, bg="#1a365d", relief="ridge", bd=3, width=320)
        sidebar_container.pack(side="right", fill="y")
        sidebar_container.pack_propagate(False)

        self.sidebar = tk.Frame(sidebar_container, bg="#f8fafc")
        self.sidebar.pack(fill="both", expand=True, padx=3, pady=3)

        # دالة عرض القسم المختار
        self.selected_section = tk.StringVar(value="المعلومات الشخصية + وصف المشروع")
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # عرض القسم الأول تلقائيًا
        self.show_section("المعلومات الشخصية + وصف المشروع")
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير PDF", command=self.export_pdf)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="حساب الإجماليات", command=self.calculate_totals)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية مع دعم RTL والخطوط العربية"""
        # رأس القائمة الجانبية مع تصميم RTL
        header_frame = tk.Frame(self.sidebar, bg=self.header_color, height=90)
        header_frame.pack(fill="x", pady=(0, 25))
        header_frame.pack_propagate(False)

        # العنوان مع محاذاة RTL
        sidebar_title = tk.Label(header_frame, text="📋 أقسام خطة العمل",
                                font=self.font_title, bg=self.header_color, fg=self.accent_color,
                                anchor="e")
        sidebar_title.pack(expand=True, padx=20)

        # خط ذهبي تحت العنوان
        gold_line = tk.Frame(self.sidebar, bg=self.accent_color, height=3)
        gold_line.pack(fill="x", padx=15, pady=(0, 20))

        # أزرار القائمة الجانبية مع دعم RTL
        for i, (icon, name) in enumerate(self.sections):
            # إطار للزر مع تصميم RTL
            btn_container = tk.Frame(self.sidebar, bg=self.sidebar_bg)
            btn_container.pack(fill="x", padx=15, pady=6)

            # إطار داخلي مع حدود ناعمة
            btn_frame = tk.Frame(btn_container, bg=self.box_bg, relief="solid", bd=1)
            btn_frame.pack(fill="x", padx=3, pady=3)

            btn = tk.Radiobutton(
                btn_frame, text=f"{name} {icon}", variable=self.selected_section, value=name,
                indicatoron=0, command=lambda n=name: self.show_section(n),
                font=self.font_normal, bg=self.box_bg, fg=self.text_color,
                activebackground=self.accent_color, activeforeground="white",
                selectcolor=self.primary_color,
                width=30, pady=18, anchor="e",  # محاذاة RTL
                relief="flat", bd=0,
                highlightthickness=0,
                cursor="hand2",
                justify="right"  # محاذاة النص RTL
            )
            btn.pack(fill="x", padx=8, pady=8)

            # تأثيرات hover ناعمة
            def on_enter(event, button=btn, frame=btn_frame):
                if button.cget('bg') != button.cget('selectcolor'):
                    button.config(bg=self.light_bg)
                    frame.config(bg=self.accent_color, relief="raised", bd=2)

            def on_leave(event, button=btn, frame=btn_frame):
                if button.cget('bg') != button.cget('selectcolor'):
                    button.config(bg=self.box_bg)
                    frame.config(bg=self.box_bg, relief="solid", bd=1)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

    def _create_eastern_header(self, parent, title, subtitle, color):
        """إنشاء رأس شرقي مع دعم RTL كامل"""
        header_container = tk.Frame(parent, bg=self.box_bg)
        header_container.pack(fill="x", pady=(0, 35))

        # إطار الرأس مع تدرج شرقي
        header_frame = tk.Frame(header_container, bg=color, relief="solid", bd=2)
        header_frame.pack(fill="x")

        # محتوى الرأس مع محاذاة RTL
        header_content = tk.Frame(header_frame, bg=color)
        header_content.pack(fill="x", padx=45, pady=30)

        # العنوان الرئيسي - محاذاة RTL كاملة
        title_label = tk.Label(header_content, text=title,
                              font=self.font_title, bg=color, fg="white",
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")

        # العنوان الفرعي - محاذاة RTL كاملة
        subtitle_label = tk.Label(header_content, text=subtitle,
                                 font=self.font_subtitle, bg=color, fg="#e2e8f0",
                                 anchor="e", justify="right")
        subtitle_label.pack(anchor="e", fill="x", pady=(10, 0))

        # خط ذهبي شرقي
        gold_line = tk.Frame(header_container, bg=self.accent_color, height=4)
        gold_line.pack(fill="x")

        return header_container

    def _create_eastern_section(self, parent, title, color):
        """إنشاء قسم شرقي متناسق"""
        # عنوان القسم مع محاذاة شرقية
        section_title_frame = tk.Frame(parent, bg="#ffffff")
        section_title_frame.pack(fill="x", pady=(25, 20))

        title_label = tk.Label(section_title_frame, text=title,
                              font=("Segoe UI", 16, "bold"), bg="#ffffff", fg=color)
        title_label.pack(anchor="e")

        # خط تحت العنوان
        title_line = tk.Frame(section_title_frame, bg=color, height=3)
        title_line.pack(fill="x", pady=(5, 0))

        # إطار القسم
        section_frame = tk.Frame(parent, bg="#f8fafc", relief="solid", bd=2)
        section_frame.pack(fill="x", pady=(0, 20))

        # محتوى القسم مع هوامش شرقية
        section_content = tk.Frame(section_frame, bg="#ffffff")
        section_content.pack(fill="x", padx=25, pady=20)

        return section_content

    def _create_eastern_field(self, parent, icon, label, placeholder, color):
        """إنشاء حقل شرقي مع دعم RTL كامل"""
        # إطار الحقل الرئيسي
        field_container = tk.Frame(parent, bg=self.box_bg)
        field_container.pack(fill="x", pady=15)

        # تسمية الحقل مع محاذاة RTL
        label_frame = tk.Frame(field_container, bg=self.box_bg)
        label_frame.pack(fill="x", pady=(0, 10))

        # أيقونة وتسمية مع ترتيب RTL
        label_content = tk.Frame(label_frame, bg=self.box_bg)
        label_content.pack(anchor="e")

        # ترتيب RTL: النص أولاً ثم الأيقونة
        tk.Label(label_content, text=label, font=self.font_normal,
                bg=self.box_bg, fg=self.text_color,
                anchor="e", justify="right").pack(side="right", padx=(0, 8))

        tk.Label(label_content, text=icon, font=("Tahoma", 14),
                bg=self.box_bg, fg=color).pack(side="right")

        # إطار الحقل مع تصميم RTL
        input_frame = tk.Frame(field_container, bg=self.light_bg, relief="solid", bd=1)
        input_frame.pack(fill="x")

        # حقل الإدخال مع دعم RTL كامل
        entry = tk.Entry(input_frame, font=self.font_arabic, relief="flat", bd=0,
                        bg=self.light_bg, fg=self.text_color, insertbackground=color,
                        justify="right")  # محاذاة النص RTL
        entry.pack(fill="x", padx=18, pady=15, ipady=8)

        # خط مؤشر شرقي
        indicator_line = tk.Frame(field_container, bg=self.border_color, height=3)
        indicator_line.pack(fill="x", pady=(5, 0))

        # تأثيرات RTL
        self._add_rtl_field_effects(entry, placeholder, indicator_line, color)

        return {"entry": entry, "indicator": indicator_line}

    def _create_rtl_section(self, parent, title, color):
        """إنشاء قسم مع دعم RTL كامل"""
        # عنوان القسم مع محاذاة RTL
        section_title_frame = tk.Frame(parent, bg=self.box_bg)
        section_title_frame.pack(fill="x", pady=(30, 25))

        title_label = tk.Label(section_title_frame, text=title,
                              font=self.font_title, bg=self.box_bg, fg=color,
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x")

        # خط تحت العنوان
        title_line = tk.Frame(section_title_frame, bg=color, height=3)
        title_line.pack(fill="x", pady=(8, 0))

        # إطار القسم
        section_frame = tk.Frame(parent, bg=self.light_bg, relief="solid", bd=1)
        section_frame.pack(fill="x", pady=(0, 25))

        # محتوى القسم مع هوامش RTL
        section_content = tk.Frame(section_frame, bg=self.box_bg)
        section_content.pack(fill="x", padx=30, pady=25)

        return section_content

    def _create_rtl_options(self, parent, icon, label):
        """إنشاء قسم خيارات مع دعم RTL"""
        # إطار الخيارات الرئيسي
        options_container = tk.Frame(parent, bg=self.box_bg)
        options_container.pack(fill="x", pady=15)

        # تسمية الخيارات مع محاذاة RTL
        label_frame = tk.Frame(options_container, bg=self.box_bg)
        label_frame.pack(fill="x", pady=(0, 18))

        label_content = tk.Frame(label_frame, bg=self.box_bg)
        label_content.pack(anchor="e")

        # ترتيب RTL: النص أولاً ثم الأيقونة
        tk.Label(label_content, text=label, font=self.font_normal,
                bg=self.box_bg, fg=self.text_color,
                anchor="e", justify="right").pack(side="right", padx=(0, 8))

        tk.Label(label_content, text=icon, font=("Tahoma", 14),
                bg=self.box_bg, fg=self.text_color).pack(side="right")

        # إطار الخيارات
        options_frame = tk.Frame(options_container, bg=self.light_bg, relief="solid", bd=1)
        options_frame.pack(fill="x")

        return options_frame

    def _create_eastern_textarea(self, parent, icon, label, placeholder, color):
        """إنشاء منطقة نص مع دعم RTL كامل"""
        # إطار المنطقة الرئيسي
        textarea_container = tk.Frame(parent, bg=self.box_bg)
        textarea_container.pack(fill="x", pady=15)

        # تسمية المنطقة مع محاذاة RTL
        label_frame = tk.Frame(textarea_container, bg=self.box_bg)
        label_frame.pack(fill="x", pady=(0, 10))

        label_content = tk.Frame(label_frame, bg=self.box_bg)
        label_content.pack(anchor="e")

        # ترتيب RTL: النص أولاً ثم الأيقونة
        tk.Label(label_content, text=label, font=self.font_normal,
                bg=self.box_bg, fg=self.text_color,
                anchor="e", justify="right").pack(side="right", padx=(0, 8))

        tk.Label(label_content, text=icon, font=("Tahoma", 14),
                bg=self.box_bg, fg=color).pack(side="right")

        # إطار منطقة النص مع تصميم RTL
        text_frame = tk.Frame(textarea_container, bg=self.light_bg, relief="solid", bd=1)
        text_frame.pack(fill="both", expand=True)

        # منطقة النص مع دعم RTL كامل
        textarea = tk.Text(text_frame, height=6, font=self.font_arabic,
                          relief="flat", bd=0, bg=self.light_bg, fg=self.text_color,
                          wrap=tk.WORD, insertbackground=color)

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=textarea.yview)
        textarea.configure(yscrollcommand=scrollbar.set)

        # ترتيب RTL: scrollbar على اليسار
        scrollbar.pack(side="left", fill="y")
        textarea.pack(side="right", fill="both", expand=True, padx=18, pady=15)

        # إضافة placeholder مع دعم RTL
        textarea.insert("1.0", placeholder)
        textarea.config(fg="#94a3b8")

        # تكوين RTL للنص
        textarea.tag_configure("rtl", justify="right")
        textarea.tag_add("rtl", "1.0", "end")

        def on_focus_in(event):
            if textarea.get("1.0", "end-1c") == placeholder:
                textarea.delete("1.0", tk.END)
                textarea.config(fg=self.text_color)
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")

        def on_focus_out(event):
            if textarea.get("1.0", "end-1c").strip() == "":
                textarea.insert("1.0", placeholder)
                textarea.config(fg="#94a3b8")
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")

        textarea.bind("<FocusIn>", on_focus_in)
        textarea.bind("<FocusOut>", on_focus_out)

        return {"textarea": textarea, "scrollbar": scrollbar}

    def _create_eastern_options(self, parent, icon, label):
        """إنشاء قسم خيارات شرقي"""
        # إطار الخيارات الرئيسي
        options_container = tk.Frame(parent, bg="#ffffff")
        options_container.pack(fill="x", pady=12)

        # تسمية الخيارات مع محاذاة شرقية
        label_frame = tk.Frame(options_container, bg="#ffffff")
        label_frame.pack(fill="x", pady=(0, 15))

        label_content = tk.Frame(label_frame, bg="#ffffff")
        label_content.pack(anchor="e")

        tk.Label(label_content, text=icon, font=("Segoe UI Emoji", 14),
                bg="#ffffff", fg="#1e293b").pack(side="right", padx=(0, 8))

        tk.Label(label_content, text=label, font=("Segoe UI", 12, "bold"),
                bg="#ffffff", fg="#1e293b").pack(side="right")

        # إطار الخيارات
        options_frame = tk.Frame(options_container, bg="#f8fafc", relief="solid", bd=2)
        options_frame.pack(fill="x")

        return options_frame

    def _add_rtl_field_effects(self, entry, placeholder, indicator, color):
        """إضافة تأثيرات RTL للحقول"""
        # إضافة placeholder مع دعم RTL
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")

        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg=self.text_color, bg=self.box_bg)
            indicator.config(bg=color)
            entry.master.config(relief="solid", bd=2, highlightbackground=color)

        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8", bg=self.light_bg)
            indicator.config(bg=self.border_color)
            entry.master.config(relief="solid", bd=1, highlightbackground=self.border_color)

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    def _create_eastern_action_buttons(self, parent):
        """إنشاء أزرار العمليات مع دعم RTL كامل"""
        buttons_container = tk.Frame(parent, bg=self.sidebar_bg)
        buttons_container.pack(fill="x", pady=30)

        # إطار الأزرار مع تصميم RTL
        buttons_frame = tk.Frame(buttons_container, bg=self.box_bg, relief="solid", bd=1)
        buttons_frame.pack(fill="x")

        # رأس الأزرار مع محاذاة RTL
        buttons_header = tk.Frame(buttons_frame, bg=self.secondary_color, height=60)
        buttons_header.pack(fill="x")
        buttons_header.pack_propagate(False)

        tk.Label(buttons_header, text="⚡ العمليات والإجراءات المتاحة",
                font=self.font_subtitle, bg=self.secondary_color, fg=self.accent_color,
                anchor="e", justify="right").pack(expand=True, fill="x", padx=25)

        # محتوى الأزرار مع ترتيب RTL
        buttons_content = tk.Frame(buttons_frame, bg=self.box_bg)
        buttons_content.pack(fill="x", padx=35, pady=30)

        buttons_data = [
            ("💾", "حفظ البيانات", self.quick_save, "#38a169"),
            ("🔄", "مسح البيانات", self.clear_current_section, "#e53e3e"),
            ("💡", "إضافة مثال", self.add_examples, "#dd6b20"),
            ("📊", "معاينة البيانات", self.preview_data, "#805ad5")
        ]

        # ترتيب الأزرار من اليمين لليسار (RTL)
        for i, (icon, text, command, color) in enumerate(buttons_data):
            btn_frame = tk.Frame(buttons_content, bg=self.light_bg, relief="solid", bd=1)
            btn_frame.pack(side="right", padx=10, pady=8)

            btn = tk.Button(btn_frame, text=f"{icon}\n{text}", command=command,
                           bg=color, fg="white", font=self.font_button,
                           relief="flat", bd=0, pady=18, padx=25, cursor="hand2",
                           activebackground=self._darken_color(color),
                           anchor="center", justify="center")
            btn.pack(fill="both", expand=True, padx=5, pady=5)

        # مساحة فارغة أنيقة
        spacer = tk.Frame(self.sidebar, bg=self.sidebar_bg, height=25)
        spacer.pack(fill="x")

        # خط فاصل ذهبي
        separator = tk.Frame(self.sidebar, bg=self.accent_color, height=3)
        separator.pack(fill="x", padx=15, pady=15)

        # أزرار سريعة مع تصميم RTL
        tools_header = tk.Frame(self.sidebar, bg=self.secondary_color, height=55)
        tools_header.pack(fill="x", padx=15, pady=(10, 0))
        tools_header.pack_propagate(False)

        tools_title = tk.Label(tools_header, text="⚡ أدوات سريعة",
                              font=self.font_subtitle, bg=self.secondary_color, fg=self.accent_color,
                              anchor="e")
        tools_title.pack(expand=True, padx=20)

        # إطار الأزرار مع تصميم RTL
        buttons_container = tk.Frame(self.sidebar, bg=self.sidebar_bg, relief="solid", bd=1)
        buttons_container.pack(fill="x", padx=15, pady=(0, 20))

        # أزرار بتصميم RTL وألوان هادئة
        buttons_data = [
            ("💾 حفظ سريع", self.quick_save, "#38a169", "white"),
            ("🔄 مسح القسم", self.clear_current_section, "#e53e3e", "white"),
            ("💡 إضافة أمثلة", self.add_examples, "#dd6b20", "white"),
            ("📊 حساب الإجماليات", self.calculate_totals, "#805ad5", "white")
        ]

        for text, command, bg_color, fg_color in buttons_data:
            btn_wrapper = tk.Frame(buttons_container, bg=self.sidebar_bg)
            btn_wrapper.pack(fill="x", pady=6, padx=10)

            btn = tk.Button(btn_wrapper, text=text, command=command,
                           bg=bg_color, fg=fg_color, font=self.font_button,
                           relief="flat", bd=0, pady=15, cursor="hand2",
                           activebackground=self._darken_color(bg_color),
                           activeforeground=fg_color,
                           anchor="e",  # محاذاة RTL
                           justify="right")  # محاذاة النص RTL
            btn.pack(fill="x", padx=5, pady=3)

            # تأثيرات hover ناعمة للأزرار
            def on_btn_enter(event, button=btn, orig_bg=bg_color):
                button.config(bg=self._darken_color(orig_bg), relief="raised", bd=2)

            def on_btn_leave(event, button=btn, orig_bg=bg_color):
                button.config(bg=orig_bg, relief="flat", bd=0)

            btn.bind("<Enter>", on_btn_enter)
            btn.bind("<Leave>", on_btn_leave)

    def _darken_color(self, color):
        """تغميق اللون للتأثيرات البصرية - محدث للتصميم الحصري"""
        color_map = {
            "#1a365d": "#0f172a",  # أزرق داكن إلى أسود مزرق
            "#dc2626": "#b91c1c",  # أحمر داكن
            "#d97706": "#c2410c",  # برتقالي داكن
            "#7c3aed": "#6d28d9",  # بنفسجي داكن
            "#27ae60": "#229954",  # أخضر داكن
            "#e74c3c": "#c0392b",  # أحمر كلاسيكي
            "#f39c12": "#e67e22",  # برتقالي كلاسيكي
            "#9b59b6": "#8e44ad"   # بنفسجي كلاسيكي
        }
        return color_map.get(color, color)

    def show_section(self, name):
        """عرض القسم المختار"""
        self.selected_section.set(name)
        for widget in self.content.winfo_children():
            widget.destroy()
            
        # إنشاء إطار قابل للتمرير
        canvas = tk.Canvas(self.content, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self.content, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # عنوان القسم مع تصميم حصري وكلاسيكي
        title_container = tk.Frame(scrollable_frame, bg="#fefefe")
        title_container.pack(fill="x", pady=(15, 25), padx=25)

        # إطار خارجي مع حدود ذهبية
        title_outer = tk.Frame(title_container, bg="#d4af37", relief="raised", bd=3)
        title_outer.pack(fill="x")

        # إطار داخلي مع تدرج لوني
        title_inner = tk.Frame(title_outer, bg="#0f172a", height=100)
        title_inner.pack(fill="x", padx=3, pady=3)
        title_inner.pack_propagate(False)

        # العنوان الرئيسي مع تأثير ذهبي
        section_title = tk.Label(title_inner, text=f"⚜️ {name} ⚜️",
                               font=("Georgia", 18, "bold"),
                               bg="#0f172a", fg="#d4af37")
        section_title.pack(expand=True)

        # خط ذهبي تحت العنوان
        gold_line = tk.Frame(title_container, bg="#d4af37", height=4)
        gold_line.pack(fill="x", pady=(3, 0))

        # خط رقيق أسفل
        thin_line = tk.Frame(title_container, bg="#1a365d", height=2)
        thin_line.pack(fill="x")

        # مساحة فارغة أنيقة
        spacer = tk.Frame(scrollable_frame, bg="#fefefe", height=15)
        spacer.pack(fill="x")
        
        # محتوى القسم حسب النوع
        if name == "المعلومات الشخصية + وصف المشروع":
            self.create_personal_info_content(scrollable_frame)
        elif name == "دراسة السوق والمنافسين":
            self.create_market_analysis_content(scrollable_frame)
        elif name == "تحليل SWOT":
            self.create_swot_analysis_content(scrollable_frame)
        elif name == "المزيج التسويقي":
            self.create_marketing_mix_content(scrollable_frame)
        elif name == "مستلزمات الإنتاج":
            self.create_production_requirements_content(scrollable_frame)
        elif name == "الدراسة المالية – التأسيسية والثابتة":
            self.create_startup_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – التشغيلية":
            self.create_operational_financial_content(scrollable_frame)
        elif name == "الدراسة المالية – ملخص شامل":
            self.create_financial_summary_content(scrollable_frame)
        elif name == "الإيرادات السنوية":
            self.create_annual_revenue_content(scrollable_frame)
        elif name == "الربح والخسارة السنوي":
            self.create_annual_profit_loss_content(scrollable_frame)
        
        scrollbar.pack(side="left", fill="y")
        canvas.pack(side="right", fill="both", expand=True)

    def create_personal_info_content(self, parent):
        """محتوى قسم المعلومات الشخصية - تصميم RTL كامل"""

        # === إطار رئيسي مع تخطيط RTL ===
        main_container = tk.Frame(parent, bg=self.sidebar_bg)
        main_container.pack(fill="both", expand=True, padx=30, pady=30)

        # === رأس القسم مع محاذاة RTL ===
        self._create_eastern_header(main_container,
                                   "👤 البيانات الشخصية والمعلومات الأساسية",
                                   "يرجى إدخال جميع المعلومات الشخصية بدقة واكتمال",
                                   self.primary_color)

        # === منطقة المحتوى RTL ===
        content_area = tk.Frame(main_container, bg=self.box_bg, relief="solid", bd=1)
        content_area.pack(fill="both", expand=True, pady=(0, 30))

        # إطار داخلي مع محاذاة RTL
        inner_content = tk.Frame(content_area, bg=self.box_bg)
        inner_content.pack(fill="both", expand=True, padx=40, pady=35)

        # === قسم المعلومات الشخصية ===
        personal_section = self._create_rtl_section(inner_content, "👤 المعلومات الشخصية", "#2563eb")

        # الحقول الشخصية مع تصميم RTL متناسق
        personal_fields = [
            ("👤", "الاسم الكامل", "full_name", "أدخل الاسم الكامل باللغة العربية", "#2563eb"),
            ("🎂", "العمر بالسنوات", "age", "أدخل العمر بالأرقام فقط", "#38a169"),
            ("🎓", "المؤهل العلمي", "education", "آخر مؤهل علمي حصلت عليه", "#805ad5"),
            ("💼", "سنوات الخبرة", "experience", "عدد سنوات الخبرة في مجال العمل", "#e53e3e"),
            ("📱", "رقم الهاتف", "phone", "رقم الهاتف مع رمز البلد (+966)", "#dd6b20"),
            ("📧", "البريد الإلكتروني", "email", "عنوان البريد الإلكتروني الصحيح", "#3182ce")
        ]

        self.personal_entries = {}

        for i, (icon, label, key, placeholder, color) in enumerate(personal_fields):
            field_container = self._create_eastern_field(personal_section, icon, label, placeholder, color)
            self.personal_entries[key] = field_container["entry"]

        # === قسم معلومات المشروع ===
        project_section = self._create_rtl_section(inner_content, "🚀 معلومات المشروع التجاري", "#3182ce")

        # اسم المشروع
        name_field = self._create_eastern_field(project_section, "🏢", "اسم المشروع التجاري",
                                               "أدخل اسم المشروع كما سيظهر رسمياً", "#3182ce")
        self.project_name_entry = name_field["entry"]

        # وصف المشروع
        desc_container = self._create_eastern_textarea(project_section, "📝", "وصف المشروع التفصيلي",
                                                      "اكتب وصفاً شاملاً ومفصلاً عن مشروعك وأهدافه", "#3182ce")
        self.project_desc_text = desc_container["textarea"]

        # === قسم التمويل ===
        funding_section = self._create_rtl_section(inner_content, "💰 خطة التمويل والاستثمار", "#e53e3e")

        # نوع التمويل
        funding_options_container = self._create_rtl_options(funding_section, "💳", "نوع التمويل المطلوب")

        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = [
            ("💼", "تمويل ذاتي", "ذاتي", "#38a169"),
            ("🏦", "قرض بنكي", "قرض بنكي", "#e53e3e"),
            ("👥", "مستثمر خارجي", "مستثمر", "#805ad5"),
            ("🔄", "تمويل مختلط", "مختلط", "#dd6b20")
        ]

        for icon, text, value, color in funding_options:
            option_card = tk.Frame(funding_options_container, bg=self.light_bg, relief="solid", bd=1)
            option_card.pack(fill="x", pady=8, padx=15)

            rb = tk.Radiobutton(option_card, text=f"{icon} {text}", variable=self.funding_type,
                              value=value, bg=self.light_bg, font=self.font_normal,
                              fg=color, selectcolor=self.accent_color,
                              activebackground=self.box_bg, cursor="hand2", pady=15,
                              anchor="e", justify="right")
            rb.pack(anchor="e", fill="x", padx=25)

        # مبلغ التمويل
        amount_field = self._create_eastern_field(funding_section, "💵", "مبلغ التمويل المطلوب (ريال سعودي)",
                                                 "أدخل المبلغ المطلوب بالأرقام فقط", "#e53e3e")
        self.funding_amount_entry = amount_field["entry"]

        # === أزرار العمليات RTL ===
        self._create_eastern_action_buttons(main_container)

        # === الجزء السفلي: معلومات المشروع والتمويل ===
        project_section = tk.Frame(main_container, bg=self.box_bg)
        project_section.pack(fill="both", expand=True, pady=(10, 0))

        # عنوان قسم المشروع
        project_title_frame = tk.Frame(project_section, bg="#d4af37", relief="raised", bd=3)
        project_title_frame.pack(fill="x", pady=(0, 15))

        project_title_inner = tk.Frame(project_title_frame, bg="#0f172a", height=60)
        project_title_inner.pack(fill="x", padx=3, pady=3)
        project_title_inner.pack_propagate(False)

        tk.Label(project_title_inner, text="🚀 معلومات المشروع والتمويل",
                font=("Georgia", 16, "bold"), bg="#0f172a", fg="#d4af37").pack(expand=True)

        # شبكة معلومات المشروع
        project_grid = tk.Frame(project_section, bg=self.box_bg)
        project_grid.pack(fill="both", expand=True)

        # === الجانب الأيمن: معلومات المشروع ===
        project_info_frame = tk.Frame(project_grid, bg="#f8fafc", relief="solid", bd=2)
        project_info_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        # رأس معلومات المشروع
        project_info_header = tk.Frame(project_info_frame, bg="#0f172a", height=50)
        project_info_header.pack(fill="x")
        project_info_header.pack_propagate(False)

        tk.Label(project_info_header, text="🏢 تفاصيل المشروع",
                font=("Georgia", 14, "bold"), bg="#0f172a", fg="#d4af37").pack(expand=True)

        # محتوى معلومات المشروع
        project_info_content = tk.Frame(project_info_frame, bg="#ffffff")
        project_info_content.pack(fill="both", expand=True, padx=15, pady=15)

        # اسم المشروع
        tk.Label(project_info_content, text="🏢 اسم المشروع:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=(0, 5))

        name_frame = tk.Frame(project_info_content, bg="#ffffff", relief="solid", bd=1)
        name_frame.pack(fill="x", pady=(0, 15))

        self.project_name_entry = tk.Entry(name_frame, font=("Segoe UI", 12), relief="flat", bd=0,
                                         bg="#ffffff", fg="#2c3e50", insertbackground="#0f172a")
        self.project_name_entry.pack(fill="x", padx=8, pady=8, ipady=5)

        # وصف المشروع
        tk.Label(project_info_content, text="📝 وصف المشروع التفصيلي:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=(0, 5))

        desc_frame = tk.Frame(project_info_content, bg="#ffffff", relief="solid", bd=1)
        desc_frame.pack(fill="both", expand=True)

        self.project_desc_text = tk.Text(desc_frame, font=("Segoe UI", 11),
                                       relief="flat", bd=0, bg="#ffffff", fg="#2c3e50",
                                       wrap=tk.WORD, insertbackground="#0f172a")

        desc_scrollbar = ttk.Scrollbar(desc_frame, orient="vertical", command=self.project_desc_text.yview)
        self.project_desc_text.configure(yscrollcommand=desc_scrollbar.set)

        self.project_desc_text.pack(side="left", fill="both", expand=True, padx=8, pady=8)
        desc_scrollbar.pack(side="right", fill="y")

        # === الجانب الأيسر: التمويل ===
        funding_frame = tk.Frame(project_grid, bg="#f8fafc", relief="solid", bd=2)
        funding_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

        # رأس التمويل
        funding_header = tk.Frame(funding_frame, bg="#1e293b", height=50)
        funding_header.pack(fill="x")
        funding_header.pack_propagate(False)

        tk.Label(funding_header, text="💰 خطة التمويل",
                font=("Georgia", 14, "bold"), bg="#1e293b", fg="#d4af37").pack(expand=True)

        # محتوى التمويل
        funding_content = tk.Frame(funding_frame, bg="#ffffff")
        funding_content.pack(fill="both", expand=True, padx=15, pady=15)

        # نوع التمويل
        tk.Label(funding_content, text="💳 نوع التمويل المطلوب:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=(0, 10))

        # خيارات التمويل
        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = [
            ("💼", "تمويل ذاتي", "ذاتي"),
            ("🏦", "قرض بنكي", "قرض بنكي"),
            ("👥", "مستثمر خارجي", "مستثمر"),
            ("🔄", "تمويل مختلط", "مختلط")
        ]

        for i, (icon, text, value) in enumerate(funding_options):
            option_frame = tk.Frame(funding_content, bg="#f8fafc", relief="solid", bd=1)
            option_frame.pack(fill="x", pady=3)

            rb = tk.Radiobutton(option_frame, text=f"{icon} {text}", variable=self.funding_type,
                              value=value, bg="#f8fafc", font=("Segoe UI", 10, "bold"),
                              fg="#1a365d", selectcolor="#d4af37",
                              activebackground="#e2e8f0", cursor="hand2", pady=8)
            rb.pack(anchor="e", padx=10)

        # مبلغ التمويل
        tk.Label(funding_content, text="💵 مبلغ التمويل المطلوب (ريال سعودي):",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=(15, 5))

        amount_frame = tk.Frame(funding_content, bg="#ffffff", relief="solid", bd=1)
        amount_frame.pack(fill="x")

        self.funding_amount_entry = tk.Entry(amount_frame, font=("Segoe UI", 12), relief="flat", bd=0,
                                           bg="#ffffff", fg="#2c3e50", insertbackground="#1e293b",
                                           justify="center")
        self.funding_amount_entry.pack(fill="x", padx=8, pady=8, ipady=5)

        # === أزرار العمليات الحصرية ===
        self._create_action_buttons(parent)

    def _create_elegant_section(self, parent, title, bg_color):
        """إنشاء قسم أنيق مع تصميم حصري"""
        # إطار القسم الرئيسي
        section_container = tk.Frame(parent, bg=self.box_bg)
        section_container.pack(fill="x", padx=20, pady=15)

        # إطار خارجي مع حدود ذهبية
        outer_frame = tk.Frame(section_container, bg="#d4af37", relief="raised", bd=3)
        outer_frame.pack(fill="x")

        # رأس القسم مع تدرج لوني
        header_frame = tk.Frame(outer_frame, bg=bg_color, height=50)
        header_frame.pack(fill="x", padx=3, pady=(3, 0))
        header_frame.pack_propagate(False)

        # العنوان مع تأثير ذهبي
        title_label = tk.Label(header_frame, text=title, font=("Georgia", 14, "bold"),
                              bg=bg_color, fg="#d4af37")
        title_label.pack(expand=True)

        # المحتوى الداخلي
        content_frame = tk.Frame(outer_frame, bg=self.box_bg)
        content_frame.pack(fill="x", padx=3, pady=(0, 3))

        return content_frame

    def _create_input_field(self, parent, icon, label, placeholder):
        """إنشاء حقل إدخال أنيق"""
        # إطار الحقل
        field_container = tk.Frame(parent, bg="#f8fafc", relief="solid", bd=1)
        field_container.pack(fill="x", pady=8)

        # رأس الحقل
        field_header = tk.Frame(field_container, bg="#1a365d", height=35)
        field_header.pack(fill="x")
        field_header.pack_propagate(False)

        header_label = tk.Label(field_header, text=f"{icon} {label}",
                              font=("Georgia", 11, "bold"), bg="#1a365d", fg="#d4af37")
        header_label.pack(expand=True)

        # حقل الإدخال
        entry_frame = tk.Frame(field_container, bg="#ffffff")
        entry_frame.pack(fill="x", padx=8, pady=8)

        entry = tk.Entry(entry_frame, font=("Segoe UI", 11), relief="flat", bd=0,
                        bg="#ffffff", fg="#2c3e50", insertbackground="#1a365d")
        entry.pack(fill="x", ipady=8)

        # خط تحت الحقل
        underline = tk.Frame(entry_frame, bg="#e2e8f0", height=2)
        underline.pack(fill="x", pady=(2, 0))

        # إضافة placeholder
        self._add_placeholder_effect(entry, placeholder, underline)

        return entry

    def _add_placeholder_effect(self, entry, placeholder, underline=None):
        """إضافة تأثير placeholder متطور"""
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")

        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg="#2c3e50")
            if underline:
                underline.config(bg="#d4af37")

        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8")
            if underline:
                underline.config(bg="#e2e8f0")

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    def _create_action_buttons(self, parent):
        """إنشاء أزرار العمليات بتصميم حصري"""
        # إطار الأزرار
        buttons_container = tk.Frame(parent, bg=self.box_bg)
        buttons_container.pack(fill="x", padx=20, pady=25)

        # خط ذهبي فاصل
        separator = tk.Frame(buttons_container, bg="#d4af37", height=3)
        separator.pack(fill="x", pady=(0, 20))

        # إطار الأزرار مع تصميم فاخر
        buttons_frame = tk.Frame(buttons_container, bg="#f8fafc", relief="groove", bd=2)
        buttons_frame.pack(fill="x")

        # رأس الأزرار
        buttons_header = tk.Frame(buttons_frame, bg="#1a365d", height=40)
        buttons_header.pack(fill="x")
        buttons_header.pack_propagate(False)

        tk.Label(buttons_header, text="⚡ عمليات سريعة", font=("Georgia", 12, "bold"),
                bg="#1a365d", fg="#d4af37").pack(expand=True)

        # الأزرار
        buttons_inner = tk.Frame(buttons_frame, bg="#ffffff")
        buttons_inner.pack(fill="x", padx=15, pady=15)

        buttons_data = [
            ("💾", "حفظ البيانات", self.quick_save, "#27ae60"),
            ("🔄", "مسح البيانات", self.clear_current_section, "#e74c3c"),
            ("💡", "إضافة مثال", self.add_examples, "#f39c12"),
            ("📊", "معاينة البيانات", self.preview_data, "#3498db")
        ]

        for i, (icon, text, command, color) in enumerate(buttons_data):
            btn_frame = tk.Frame(buttons_inner, bg="#f8fafc", relief="solid", bd=1)
            btn_frame.grid(row=0, column=i, padx=8, pady=5, sticky="ew")
            buttons_inner.grid_columnconfigure(i, weight=1)

            btn = tk.Button(btn_frame, text=f"{icon}\n{text}", command=command,
                           bg=color, fg="white", font=("Segoe UI", 10, "bold"),
                           relief="raised", bd=2, pady=15, cursor="hand2",
                           activebackground=self._darken_color(color))
            btn.pack(fill="both", expand=True, padx=3, pady=3)

    def preview_data(self):
        """معاينة البيانات المدخلة"""
        messagebox.showinfo("معاينة البيانات", "ميزة معاينة البيانات ستكون متاحة قريباً")

    def _create_professional_header(self, parent, title, subtitle, color):
        """إنشاء رأس احترافي للأقسام"""
        header_container = tk.Frame(parent, bg="#ffffff")
        header_container.pack(fill="x", pady=(0, 25))

        # إطار الرأس مع تدرج
        header_frame = tk.Frame(header_container, bg=color, relief="flat", bd=0)
        header_frame.pack(fill="x")

        # محتوى الرأس
        header_content = tk.Frame(header_frame, bg=color)
        header_content.pack(fill="x", padx=30, pady=20)

        # العنوان الرئيسي
        title_label = tk.Label(header_content, text=title,
                              font=("Segoe UI", 20, "bold"), bg=color, fg="white")
        title_label.pack(anchor="center")

        # العنوان الفرعي
        subtitle_label = tk.Label(header_content, text=subtitle,
                                 font=("Segoe UI", 11), bg=color, fg="#e2e8f0")
        subtitle_label.pack(anchor="center", pady=(5, 0))

        # خط ذهبي تحت الرأس
        gold_line = tk.Frame(header_container, bg="#d4af37", height=3)
        gold_line.pack(fill="x")

        return header_container

    def _add_professional_field_effects(self, entry, placeholder, indicator, color):
        """إضافة تأثيرات احترافية للحقول"""
        # إضافة placeholder
        entry.insert(0, placeholder)
        entry.config(fg="#94a3b8")

        def on_focus_in(event):
            if entry.get() == placeholder:
                entry.delete(0, tk.END)
                entry.config(fg="#1e293b", bg="#ffffff")
            indicator.config(bg=color)
            entry.master.config(relief="solid", bd=2, highlightbackground=color)

        def on_focus_out(event):
            if entry.get() == "":
                entry.insert(0, placeholder)
                entry.config(fg="#94a3b8", bg="#f1f5f9")
            indicator.config(bg="#e2e8f0")
            entry.master.config(relief="solid", bd=1, highlightbackground="#e2e8f0")

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)

    def _create_project_section(self, parent):
        """إنشاء قسم معلومات المشروع بتصميم احترافي"""
        # عنوان القسم
        section_title = tk.Label(parent, text="🚀 معلومات المشروع",
                               font=("Segoe UI", 16, "bold"), bg="#ffffff", fg="#1e293b")
        section_title.pack(anchor="center", pady=(20, 15))

        # إطار المشروع
        project_frame = tk.Frame(parent, bg="#f8fafc", relief="solid", bd=1)
        project_frame.pack(fill="x", pady=(0, 20))

        project_content = tk.Frame(project_frame, bg="#ffffff")
        project_content.pack(fill="x", padx=25, pady=20)

        # اسم المشروع
        name_section = self._create_input_section(project_content, "🏢", "اسم المشروع",
                                                 "أدخل اسم مشروعك التجاري", "#0891b2")
        self.project_name_entry = name_section["entry"]

        # وصف المشروع
        desc_label = tk.Label(project_content, text="📝 وصف المشروع التفصيلي",
                             font=("Segoe UI", 12, "bold"), bg="#ffffff", fg="#1e293b")
        desc_label.pack(anchor="e", pady=(15, 8))

        desc_frame = tk.Frame(project_content, bg="#f1f5f9", relief="solid", bd=1)
        desc_frame.pack(fill="x", pady=(0, 10))

        self.project_desc_text = tk.Text(desc_frame, height=4, font=("Segoe UI", 11),
                                       relief="flat", bd=0, bg="#f1f5f9", fg="#1e293b",
                                       wrap=tk.WORD, insertbackground="#0891b2")

        desc_scroll = ttk.Scrollbar(desc_frame, orient="vertical", command=self.project_desc_text.yview)
        self.project_desc_text.configure(yscrollcommand=desc_scroll.set)

        self.project_desc_text.pack(side="left", fill="both", expand=True, padx=12, pady=10)
        desc_scroll.pack(side="right", fill="y")

        return project_frame

    def _create_funding_section(self, parent):
        """إنشاء قسم التمويل بتصميم احترافي"""
        # عنوان القسم
        section_title = tk.Label(parent, text="💰 خطة التمويل",
                               font=("Segoe UI", 16, "bold"), bg="#ffffff", fg="#1e293b")
        section_title.pack(anchor="center", pady=(20, 15))

        # إطار التمويل
        funding_frame = tk.Frame(parent, bg="#f8fafc", relief="solid", bd=1)
        funding_frame.pack(fill="x", pady=(0, 20))

        funding_content = tk.Frame(funding_frame, bg="#ffffff")
        funding_content.pack(fill="x", padx=25, pady=20)

        # نوع التمويل
        type_label = tk.Label(funding_content, text="💳 نوع التمويل المطلوب",
                             font=("Segoe UI", 12, "bold"), bg="#ffffff", fg="#1e293b")
        type_label.pack(anchor="center", pady=(0, 15))

        # خيارات التمويل في شبكة احترافية
        options_grid = tk.Frame(funding_content, bg="#ffffff")
        options_grid.pack(fill="x", pady=(0, 20))

        self.funding_type = tk.StringVar(value="ذاتي")
        funding_options = [
            ("💼", "تمويل ذاتي", "ذاتي", "#059669"),
            ("🏦", "قرض بنكي", "قرض بنكي", "#dc2626"),
            ("👥", "مستثمر خارجي", "مستثمر", "#7c3aed"),
            ("🔄", "تمويل مختلط", "مختلط", "#ea580c")
        ]

        for i, (icon, text, value, color) in enumerate(funding_options):
            option_card = tk.Frame(options_grid, bg="#f8fafc", relief="solid", bd=1)
            option_card.grid(row=0, column=i, padx=8, pady=5, sticky="ew")
            options_grid.grid_columnconfigure(i, weight=1)

            rb = tk.Radiobutton(option_card, text=f"{icon}\n{text}", variable=self.funding_type,
                              value=value, bg="#f8fafc", font=("Segoe UI", 10, "bold"),
                              fg=color, selectcolor="#d4af37", indicatoron=0,
                              activebackground="#e2e8f0", cursor="hand2", pady=12)
            rb.pack(fill="both", expand=True)

        # مبلغ التمويل
        amount_section = self._create_input_section(funding_content, "💵", "مبلغ التمويل (ريال سعودي)",
                                                   "أدخل المبلغ المطلوب", "#dc2626")
        self.funding_amount_entry = amount_section["entry"]

        return funding_frame

    def _create_input_section(self, parent, icon, label, placeholder, color):
        """إنشاء قسم إدخال احترافي"""
        section_frame = tk.Frame(parent, bg="#ffffff")
        section_frame.pack(fill="x", pady=8)

        # تسمية مع أيقونة
        label_frame = tk.Frame(section_frame, bg="#ffffff")
        label_frame.pack(fill="x", pady=(0, 8))

        tk.Label(label_frame, text=icon, font=("Segoe UI Emoji", 14),
                bg="#ffffff", fg=color).pack(side="right", padx=(0, 8))

        tk.Label(label_frame, text=label, font=("Segoe UI", 12, "bold"),
                bg="#ffffff", fg="#1e293b").pack(anchor="e")

        # حقل الإدخال
        input_container = tk.Frame(section_frame, bg="#f1f5f9", relief="solid", bd=1)
        input_container.pack(fill="x")

        entry = tk.Entry(input_container, font=("Segoe UI", 11), relief="flat", bd=0,
                        bg="#f1f5f9", fg="#1e293b", insertbackground=color)
        entry.pack(fill="x", padx=12, pady=8, ipady=3)

        # خط مؤشر
        indicator = tk.Frame(section_frame, bg="#e2e8f0", height=2)
        indicator.pack(fill="x", pady=(2, 0))

        # تأثيرات تفاعلية
        self._add_professional_field_effects(entry, placeholder, indicator, color)

        return {"entry": entry, "indicator": indicator}

    def _create_professional_action_buttons(self, parent):
        """إنشاء أزرار العمليات بتصميم احترافي"""
        buttons_container = tk.Frame(parent, bg="#f8fafc")
        buttons_container.pack(fill="x", pady=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(buttons_container, bg="#ffffff", relief="solid", bd=1)
        buttons_frame.pack(fill="x")

        # رأس الأزرار
        buttons_header = tk.Frame(buttons_frame, bg="#1e293b", height=50)
        buttons_header.pack(fill="x")
        buttons_header.pack_propagate(False)

        tk.Label(buttons_header, text="⚡ العمليات المتاحة",
                font=("Segoe UI", 14, "bold"), bg="#1e293b", fg="#d4af37").pack(expand=True)

        # الأزرار
        buttons_content = tk.Frame(buttons_frame, bg="#ffffff")
        buttons_content.pack(fill="x", padx=30, pady=20)

        buttons_data = [
            ("💾", "حفظ البيانات", self.quick_save, "#059669"),
            ("🔄", "مسح البيانات", self.clear_current_section, "#dc2626"),
            ("💡", "إضافة مثال", self.add_examples, "#ea580c"),
            ("📊", "معاينة البيانات", self.preview_data, "#7c3aed")
        ]

        for i, (icon, text, command, color) in enumerate(buttons_data):
            btn_frame = tk.Frame(buttons_content, bg="#f8fafc", relief="solid", bd=1)
            btn_frame.grid(row=0, column=i, padx=10, pady=5, sticky="ew")
            buttons_content.grid_columnconfigure(i, weight=1)

            btn = tk.Button(btn_frame, text=f"{icon}\n{text}", command=command,
                           bg=color, fg="white", font=("Segoe UI", 10, "bold"),
                           relief="flat", bd=0, pady=15, cursor="hand2",
                           activebackground=self._darken_color(color))
            btn.pack(fill="both", expand=True, padx=3, pady=3)

            # تأثيرات hover
            def on_enter(event, button=btn, orig_color=color):
                button.config(bg=self._darken_color(orig_color))

            def on_leave(event, button=btn, orig_color=color):
                button.config(bg=orig_color)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

    def create_market_analysis_content(self, parent):
        """محتوى قسم دراسة السوق - تصميم شرقي عالي الجودة"""

        # === إطار رئيسي مع تخطيط شرقي ===
        main_container = tk.Frame(parent, bg="#f8fafc")
        main_container.pack(fill="both", expand=True, padx=25, pady=25)

        # === رأس القسم مع محاذاة شرقية ===
        self._create_eastern_header(main_container,
                                   "📊 دراسة السوق والمنافسين التفصيلية",
                                   "قم بتحليل السوق المستهدف ودراسة المنافسين بدقة واكتمال",
                                   "#0891b2")

        # === منطقة المحتوى الشرقية ===
        content_area = tk.Frame(main_container, bg="#ffffff", relief="solid", bd=2)
        content_area.pack(fill="both", expand=True, pady=(0, 25))

        # إطار داخلي مع محاذاة شرقية
        inner_content = tk.Frame(content_area, bg="#ffffff")
        inner_content.pack(fill="both", expand=True, padx=35, pady=30)

        # === قسم المنتجات والخدمات ===
        products_section = self._create_eastern_section(inner_content, "🛍️ المنتجات والخدمات المقدمة", "#2563eb")

        # المنتجات
        products_field = self._create_eastern_textarea(products_section, "🛍️", "المنتجات التي سيقدمها المشروع",
                                                      "اذكر جميع المنتجات التي سيقدمها مشروعك بالتفصيل (منتج واحد في كل سطر)", "#2563eb")
        self.products_entry = products_field["textarea"]

        # الخدمات
        services_field = self._create_eastern_textarea(products_section, "🔧", "الخدمات التي سيقدمها المشروع",
                                                      "اذكر جميع الخدمات التي سيقدمها مشروعك بالتفصيل (خدمة واحدة في كل سطر)", "#7c3aed")
        self.services_entry = services_field["textarea"]

        # === قسم تحليل المنافسين ===
        competitors_section = self._create_eastern_section(inner_content, "⚔️ تحليل المنافسين في السوق", "#dc2626")

        # وجود المنافسين
        competitors_existence = self._create_eastern_options(competitors_section, "❓", "هل يوجد منافسون مباشرون لمشروعك في السوق؟")

        self.has_competitors = tk.StringVar(value="نعم")

        # خيار نعم
        yes_frame = tk.Frame(competitors_existence, bg="#dcfce7", relief="solid", bd=2)
        yes_frame.pack(fill="x", pady=8, padx=15)

        yes_rb = tk.Radiobutton(yes_frame, text="✅ نعم، يوجد منافسون مباشرون في السوق",
                               variable=self.has_competitors, value="نعم",
                               bg="#dcfce7", font=("Segoe UI", 12, "bold"),
                               fg="#059669", selectcolor="#d4af37", cursor="hand2", pady=15)
        yes_rb.pack(anchor="e", padx=20)

        # خيار لا
        no_frame = tk.Frame(competitors_existence, bg="#fef2f2", relief="solid", bd=2)
        no_frame.pack(fill="x", pady=8, padx=15)

        no_rb = tk.Radiobutton(no_frame, text="❌ لا، لا يوجد منافسون مباشرون في السوق",
                              variable=self.has_competitors, value="لا",
                              bg="#fef2f2", font=("Segoe UI", 12, "bold"),
                              fg="#dc2626", selectcolor="#d4af37", cursor="hand2", pady=15)
        no_rb.pack(anchor="e", padx=20)

        # عدد المنافسين
        count_field = self._create_eastern_field(competitors_section, "🔢", "عدد المنافسين الرئيسيين في السوق",
                                                "أدخل عدد المنافسين المباشرين (بالأرقام فقط)", "#dc2626")
        self.competitor_count = count_field["entry"]

        # المنتجات والخدمات المنافسة
        comp_products_field = self._create_eastern_textarea(competitors_section, "🏪", "المنتجات والخدمات التي يقدمها المنافسون",
                                                           "اذكر بالتفصيل المنتجات والخدمات التي يقدمها المنافسون في السوق", "#ea580c")
        self.comp_products_entry = comp_products_field["textarea"]

        # === أزرار العمليات الشرقية ===
        self._create_eastern_action_buttons(main_container)

    def _create_products_services_section(self, parent):
        """إنشاء قسم المنتجات والخدمات بتصميم احترافي"""
        # عنوان القسم
        section_title = tk.Label(parent, text="🛍️ المنتجات والخدمات المقدمة",
                               font=("Segoe UI", 16, "bold"), bg="#ffffff", fg="#1e293b")
        section_title.pack(anchor="center", pady=(0, 20))

        # شبكة المنتجات والخدمات
        grid_container = tk.Frame(parent, bg="#ffffff")
        grid_container.pack(fill="both", expand=True, pady=(0, 25))

        # === المنتجات (الجانب الأيمن) ===
        products_card = tk.Frame(grid_container, bg="#f1f5f9", relief="solid", bd=2)
        products_card.pack(side="right", fill="both", expand=True, padx=(10, 0))

        # رأس بطاقة المنتجات
        products_header = tk.Frame(products_card, bg="#0891b2", height=55)
        products_header.pack(fill="x")
        products_header.pack_propagate(False)

        tk.Label(products_header, text="🛍️ المنتجات",
                font=("Segoe UI", 14, "bold"), bg="#0891b2", fg="white").pack(expand=True)

        # محتوى المنتجات
        products_content = tk.Frame(products_card, bg="#ffffff")
        products_content.pack(fill="both", expand=True, padx=20, pady=20)

        tk.Label(products_content, text="📝 اذكر جميع المنتجات التي سيقدمها مشروعك:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#1e293b").pack(anchor="e", pady=(0, 10))

        products_frame = tk.Frame(products_content, bg="#f1f5f9", relief="solid", bd=1)
        products_frame.pack(fill="both", expand=True)

        self.products_entry = tk.Text(products_frame, font=("Segoe UI", 11),
                                    relief="flat", bd=0, bg="#f1f5f9", fg="#1e293b",
                                    wrap=tk.WORD, insertbackground="#0891b2")

        products_scroll = ttk.Scrollbar(products_frame, orient="vertical", command=self.products_entry.yview)
        self.products_entry.configure(yscrollcommand=products_scroll.set)

        self.products_entry.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        products_scroll.pack(side="right", fill="y")

        # === الخدمات (الجانب الأيسر) ===
        services_card = tk.Frame(grid_container, bg="#f1f5f9", relief="solid", bd=2)
        services_card.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # رأس بطاقة الخدمات
        services_header = tk.Frame(services_card, bg="#7c3aed", height=55)
        services_header.pack(fill="x")
        services_header.pack_propagate(False)

        tk.Label(services_header, text="🔧 الخدمات",
                font=("Segoe UI", 14, "bold"), bg="#7c3aed", fg="white").pack(expand=True)

        # محتوى الخدمات
        services_content = tk.Frame(services_card, bg="#ffffff")
        services_content.pack(fill="both", expand=True, padx=20, pady=20)

        tk.Label(services_content, text="🔧 اذكر جميع الخدمات التي سيقدمها مشروعك:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#1e293b").pack(anchor="e", pady=(0, 10))

        services_frame = tk.Frame(services_content, bg="#f1f5f9", relief="solid", bd=1)
        services_frame.pack(fill="both", expand=True)

        self.services_entry = tk.Text(services_frame, font=("Segoe UI", 11),
                                    relief="flat", bd=0, bg="#f1f5f9", fg="#1e293b",
                                    wrap=tk.WORD, insertbackground="#7c3aed")

        services_scroll = ttk.Scrollbar(services_frame, orient="vertical", command=self.services_entry.yview)
        self.services_entry.configure(yscrollcommand=services_scroll.set)

        self.services_entry.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        services_scroll.pack(side="right", fill="y")

        return grid_container

    def _create_competitors_analysis_section(self, parent):
        """إنشاء قسم تحليل المنافسين بتصميم احترافي"""
        # عنوان القسم
        section_title = tk.Label(parent, text="⚔️ تحليل المنافسين والسوق",
                               font=("Segoe UI", 16, "bold"), bg="#ffffff", fg="#1e293b")
        section_title.pack(anchor="center", pady=(0, 20))

        # شبكة تحليل المنافسين
        analysis_grid = tk.Frame(parent, bg="#ffffff")
        analysis_grid.pack(fill="both", expand=True)

        # === معلومات المنافسين (الجانب الأيمن) ===
        competitors_info_card = tk.Frame(analysis_grid, bg="#f1f5f9", relief="solid", bd=2)
        competitors_info_card.pack(side="right", fill="both", expand=True, padx=(10, 0))

        # رأس البطاقة
        info_header = tk.Frame(competitors_info_card, bg="#dc2626", height=55)
        info_header.pack(fill="x")
        info_header.pack_propagate(False)

        tk.Label(info_header, text="📊 معلومات المنافسين",
                font=("Segoe UI", 14, "bold"), bg="#dc2626", fg="white").pack(expand=True)

        # محتوى البطاقة
        info_content = tk.Frame(competitors_info_card, bg="#ffffff")
        info_content.pack(fill="both", expand=True, padx=20, pady=20)

        # سؤال وجود المنافسين
        tk.Label(info_content, text="❓ هل يوجد منافسون مباشرون لمشروعك؟",
                font=("Segoe UI", 12, "bold"), bg="#ffffff", fg="#1e293b").pack(anchor="center", pady=(0, 15))

        # خيارات وجود المنافسين
        self.has_competitors = tk.StringVar(value="نعم")

        yes_option = tk.Frame(info_content, bg="#dcfce7", relief="solid", bd=2)
        yes_option.pack(fill="x", pady=5)

        tk.Radiobutton(yes_option, text="✅ نعم، يوجد منافسون مباشرون", variable=self.has_competitors,
                      value="نعم", bg="#dcfce7", font=("Segoe UI", 11, "bold"),
                      fg="#059669", selectcolor="#d4af37", cursor="hand2", pady=10).pack()

        no_option = tk.Frame(info_content, bg="#fef2f2", relief="solid", bd=2)
        no_option.pack(fill="x", pady=5)

        tk.Radiobutton(no_option, text="❌ لا، لا يوجد منافسون مباشرون", variable=self.has_competitors,
                      value="لا", bg="#fef2f2", font=("Segoe UI", 11, "bold"),
                      fg="#dc2626", selectcolor="#d4af37", cursor="hand2", pady=10).pack()

        # عدد المنافسين
        count_section = self._create_input_section(info_content, "🔢", "عدد المنافسين الرئيسيين",
                                                  "أدخل العدد", "#dc2626")
        self.competitor_count = count_section["entry"]

        # === المنتجات المنافسة (الجانب الأيسر) ===
        products_analysis_card = tk.Frame(analysis_grid, bg="#f1f5f9", relief="solid", bd=2)
        products_analysis_card.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # رأس البطاقة
        products_header = tk.Frame(products_analysis_card, bg="#ea580c", height=55)
        products_header.pack(fill="x")
        products_header.pack_propagate(False)

        tk.Label(products_header, text="🏪 المنتجات المنافسة",
                font=("Segoe UI", 14, "bold"), bg="#ea580c", fg="white").pack(expand=True)

        # محتوى البطاقة
        products_content = tk.Frame(products_analysis_card, bg="#ffffff")
        products_content.pack(fill="both", expand=True, padx=20, pady=20)

        tk.Label(products_content, text="📝 اذكر المنتجات والخدمات التي يقدمها المنافسون:",
                font=("Segoe UI", 11, "bold"), bg="#ffffff", fg="#1e293b").pack(anchor="e", pady=(0, 10))

        comp_frame = tk.Frame(products_content, bg="#f1f5f9", relief="solid", bd=1)
        comp_frame.pack(fill="both", expand=True)

        self.comp_products_entry = tk.Text(comp_frame, font=("Segoe UI", 11),
                                         relief="flat", bd=0, bg="#f1f5f9", fg="#1e293b",
                                         wrap=tk.WORD, insertbackground="#ea580c")

        comp_scroll = ttk.Scrollbar(comp_frame, orient="vertical", command=self.comp_products_entry.yview)
        self.comp_products_entry.configure(yscrollcommand=comp_scroll.set)

        self.comp_products_entry.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        comp_scroll.pack(side="right", fill="y")

        return analysis_grid

    def create_swot_analysis_content(self, parent):
        """محتوى قسم تحليل SWOT - تصميم شرقي عالي الجودة"""

        # === إطار رئيسي مع تخطيط شرقي ===
        main_container = tk.Frame(parent, bg="#f8fafc")
        main_container.pack(fill="both", expand=True, padx=25, pady=25)

        # === رأس القسم مع محاذاة شرقية ===
        self._create_eastern_header(main_container,
                                   "⚖️ تحليل SWOT الاستراتيجي الشامل",
                                   "قم بتحليل نقاط القوة والضعف والفرص والتهديدات لمشروعك بدقة",
                                   "#7c3aed")

        # === منطقة المحتوى الشرقية ===
        content_area = tk.Frame(main_container, bg="#ffffff", relief="solid", bd=2)
        content_area.pack(fill="both", expand=True, pady=(0, 25))

        # إطار داخلي مع محاذاة شرقية
        inner_content = tk.Frame(content_area, bg="#ffffff")
        inner_content.pack(fill="both", expand=True, padx=35, pady=30)

        # === شبكة SWOT الشرقية ===
        swot_grid_container = tk.Frame(inner_content, bg="#ffffff")
        swot_grid_container.pack(fill="both", expand=True)

        # إطار خارجي مع حدود ذهبية
        swot_outer = tk.Frame(swot_grid_container, bg="#d4af37", relief="solid", bd=4)
        swot_outer.pack(fill="both", expand=True)

        # الشبكة الداخلية مع ترتيب شرقي
        swot_grid = tk.Frame(swot_outer, bg="#f8fafc")
        swot_grid.pack(fill="both", expand=True, padx=4, pady=4)

        # === نقاط القوة (أعلى يمين) ===
        strengths_result = self._create_swot_card(swot_grid, "💪", "نقاط القوة", "Strengths",
                                                   "اذكر جميع نقاط القوة في مشروعك", "#38a169", "#dcfce7")
        strengths_result["container"].grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        self.strengths_text = strengths_result["textarea"]

        # === نقاط الضعف (أعلى يسار) ===
        weaknesses_result = self._create_swot_card(swot_grid, "⚠️", "نقاط الضعف", "Weaknesses",
                                                    "اذكر جميع نقاط الضعف في مشروعك", "#e53e3e", "#fef2f2")
        weaknesses_result["container"].grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.weaknesses_text = weaknesses_result["textarea"]

        # === الفرص (أسفل يمين) ===
        opportunities_result = self._create_swot_card(swot_grid, "🌟", "الفرص المتاحة", "Opportunities",
                                                       "اذكر جميع الفرص المتاحة لمشروعك", "#3182ce", "#dbeafe")
        opportunities_result["container"].grid(row=1, column=1, padx=5, pady=5, sticky="nsew")
        self.opportunities_text = opportunities_result["textarea"]

        # === التهديدات (أسفل يسار) ===
        threats_result = self._create_swot_card(swot_grid, "⚡", "التهديدات المحتملة", "Threats",
                                                 "اذكر جميع التهديدات التي قد تواجه مشروعك", "#dd6b20", "#fed7aa")
        threats_result["container"].grid(row=1, column=0, padx=5, pady=5, sticky="nsew")
        self.threats_text = threats_result["textarea"]

        # تكوين الشبكة للتوسع المتساوي
        swot_grid.grid_rowconfigure(0, weight=1)
        swot_grid.grid_rowconfigure(1, weight=1)
        swot_grid.grid_columnconfigure(0, weight=1)
        swot_grid.grid_columnconfigure(1, weight=1)

        # === أزرار العمليات الشرقية ===
        self._create_eastern_action_buttons(main_container)

    def _create_swot_card(self, parent, icon, title_ar, title_en, placeholder, color, bg_color):
        """إنشاء بطاقة SWOT مع دعم RTL كامل"""
        # إطار البطاقة الرئيسي
        card_container = tk.Frame(parent, bg=bg_color, relief="solid", bd=2)

        # رأس البطاقة مع تدرج RTL
        card_header = tk.Frame(card_container, bg=color, height=75)
        card_header.pack(fill="x")
        card_header.pack_propagate(False)

        # محتوى الرأس مع محاذاة RTL
        header_content = tk.Frame(card_header, bg=color)
        header_content.pack(expand=True, fill="both", padx=20)

        # العنوان العربي مع محاذاة RTL
        tk.Label(header_content, text=f"{icon} {title_ar}",
                font=self.font_subtitle, bg=color, fg="white",
                anchor="e", justify="right").pack(anchor="e", fill="x", pady=(10, 3))

        # العنوان الإنجليزي مع محاذاة RTL
        tk.Label(header_content, text=title_en,
                font=self.font_small, bg=color, fg="#f1f5f9",
                anchor="e", justify="right").pack(anchor="e", fill="x", pady=(0, 10))

        # محتوى البطاقة
        card_body = tk.Frame(card_container, bg=self.box_bg)
        card_body.pack(fill="both", expand=True, padx=18, pady=18)

        # تسمية توضيحية مع محاذاة RTL
        tk.Label(card_body, text="📝 التفاصيل:",
                font=self.font_normal, bg=self.box_bg, fg=color,
                anchor="e", justify="right").pack(anchor="e", fill="x", pady=(0, 10))

        # إطار منطقة النص
        text_frame = tk.Frame(card_body, bg=self.light_bg, relief="solid", bd=1)
        text_frame.pack(fill="both", expand=True)

        # منطقة النص مع دعم RTL كامل
        textarea = tk.Text(text_frame, font=self.font_arabic,
                          relief="flat", bd=0, bg=self.light_bg, fg=self.text_color,
                          wrap=tk.WORD, insertbackground=color)

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=textarea.yview)
        textarea.configure(yscrollcommand=scrollbar.set)

        # ترتيب RTL: scrollbar على اليسار
        scrollbar.pack(side="left", fill="y")
        textarea.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        # إضافة placeholder مع دعم RTL
        textarea.insert("1.0", placeholder)
        textarea.config(fg="#94a3b8")

        # تكوين RTL للنص
        textarea.tag_configure("rtl", justify="right")
        textarea.tag_add("rtl", "1.0", "end")

        def on_focus_in(event):
            if textarea.get("1.0", "end-1c") == placeholder:
                textarea.delete("1.0", tk.END)
                textarea.config(fg=self.text_color)
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")

        def on_focus_out(event):
            if textarea.get("1.0", "end-1c").strip() == "":
                textarea.insert("1.0", placeholder)
                textarea.config(fg="#94a3b8")
                textarea.tag_configure("rtl", justify="right")
                textarea.tag_add("rtl", "1.0", "end")

        textarea.bind("<FocusIn>", on_focus_in)
        textarea.bind("<FocusOut>", on_focus_out)

        return {"container": card_container, "textarea": textarea}

    def create_marketing_mix_content(self, parent):
        """محتوى قسم المزيج التسويقي"""
        # المنتج
        product_frame = tk.LabelFrame(parent, text="🛍️ المنتج",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        product_frame.pack(fill="x", padx=20, pady=10)

        self.product_text = tk.Text(product_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.product_text.pack(fill="x", padx=10, pady=10)

        # السعر
        price_frame = tk.LabelFrame(parent, text="💰 السعر",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        price_frame.pack(fill="x", padx=20, pady=10)

        self.price_text = tk.Text(price_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.price_text.pack(fill="x", padx=10, pady=10)

        # المكان
        place_frame = tk.LabelFrame(parent, text="📍 المكان",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        place_frame.pack(fill="x", padx=20, pady=10)

        self.place_text = tk.Text(place_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.place_text.pack(fill="x", padx=10, pady=10)

        # الترويج
        promotion_frame = tk.LabelFrame(parent, text="📣 الترويج",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=20, pady=10)

        self.promotion_text = tk.Text(promotion_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.promotion_text.pack(fill="x", padx=10, pady=10)

        # الناس
        people_frame = tk.LabelFrame(parent, text="👥 الناس",
                                   font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        people_frame.pack(fill="x", padx=20, pady=10)

        self.people_text = tk.Text(people_frame, height=5, font=self.font_small, wrap=tk.WORD)
        self.people_text.pack(fill="x", padx=10, pady=10)

    def create_production_requirements_content(self, parent):
        """محتوى قسم مستلزمات الإنتاج"""
        # المعدات والآلات
        equipment_frame = tk.LabelFrame(parent, text="🔧 المعدات والآلات",
                                      font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        equipment_frame.pack(fill="x", padx=20, pady=10)

        # جدول المعدات
        equipment_table_frame = tk.Frame(equipment_frame, bg=self.box_bg)
        equipment_table_frame.pack(fill="x", padx=10, pady=10)

        # عناوين الجدول
        headers = ["المعدة/الآلة", "الكمية", "السعر الواحد", "الإجمالي"]
        for i, header in enumerate(headers):
            tk.Label(equipment_table_frame, text=header, font=self.font_normal,
                    bg="#e0e0e0", relief="ridge").grid(row=0, column=i, sticky="ew", padx=1, pady=1)

        # صفوف المعدات
        self.equipment_entries = []
        for row in range(1, 6):  # 5 صفوف للمعدات
            row_entries = []
            for col in range(4):
                if col == 3:  # عمود الإجمالي
                    entry = tk.Label(equipment_table_frame, text="0", bg="white", relief="sunken")
                else:
                    entry = tk.Entry(equipment_table_frame, width=15, font=self.font_small)
                    if col in [1, 2]:  # أعمدة الكمية والسعر
                        entry.bind('<KeyRelease>', lambda e, r=row-1: self.calculate_equipment_total(r))
                entry.grid(row=row, column=col, sticky="ew", padx=1, pady=1)
                row_entries.append(entry)
            self.equipment_entries.append(row_entries)

        # إجمالي المعدات
        tk.Label(equipment_table_frame, text="إجمالي المعدات:", font=self.font_normal,
                bg="#e0e0e0").grid(row=6, column=2, sticky="e", padx=5, pady=5)
        self.total_equipment_label = tk.Label(equipment_table_frame, text="0", font=self.font_normal,
                                            bg="yellow", relief="sunken")
        self.total_equipment_label.grid(row=6, column=3, sticky="ew", padx=1, pady=1)

    def create_startup_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التأسيسية"""
        costs_frame = tk.LabelFrame(parent, text="التكاليف التأسيسية والثابتة",
                                  font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        costs_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.startup_costs_text = tk.Text(costs_frame, font=self.font_small, wrap=tk.WORD)
        self.startup_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_operational_financial_content(self, parent):
        """محتوى قسم الدراسة المالية التشغيلية"""
        operational_frame = tk.LabelFrame(parent, text="التكاليف التشغيلية الشهرية",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        operational_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.operational_costs_text = tk.Text(operational_frame, font=self.font_small, wrap=tk.WORD)
        self.operational_costs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_financial_summary_content(self, parent):
        """محتوى قسم الدراسة المالية الملخص الشامل"""
        summary_frame = tk.LabelFrame(parent, text="الملخص المالي الشامل",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        summary_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.financial_summary_text = tk.Text(summary_frame, font=self.font_small, wrap=tk.WORD)
        self.financial_summary_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_revenue_content(self, parent):
        """محتوى قسم الإيرادات السنوية"""
        revenue_frame = tk.LabelFrame(parent, text="الإيرادات السنوية المتوقعة",
                                    font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        revenue_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.annual_revenue_text = tk.Text(revenue_frame, font=self.font_small, wrap=tk.WORD)
        self.annual_revenue_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_annual_profit_loss_content(self, parent):
        """محتوى قسم الربح والخسارة السنوي"""
        profit_loss_frame = tk.LabelFrame(parent, text="تحليل الربح والخسارة السنوي",
                                        font=self.font_normal, bg=self.box_bg, relief="groove", bd=2)
        profit_loss_frame.pack(fill="both", expand=True, padx=20, pady=20)

        self.profit_loss_text = tk.Text(profit_loss_frame, font=self.font_small, wrap=tk.WORD)
        self.profit_loss_text.pack(fill="both", expand=True, padx=10, pady=10)

    # الوظائف المساعدة
    def calculate_equipment_total(self, row):
        """حساب إجمالي المعدة في الصف المحدد"""
        try:
            quantity = float(self.equipment_entries[row][1].get() or 0)
            price = float(self.equipment_entries[row][2].get() or 0)
            total = quantity * price
            self.equipment_entries[row][3].config(text=f"{total:.2f}")
            self.calculate_production_totals()
        except (ValueError, IndexError):
            pass

    def calculate_production_totals(self):
        """حساب الإجماليات الكلية"""
        equipment_total = 0
        for row in self.equipment_entries:
            try:
                equipment_total += float(row[3].cget("text"))
            except (ValueError, AttributeError):
                pass
        self.total_equipment_label.config(text=f"{equipment_total:.2f}")

    def calculate_totals(self):
        """حساب جميع الإجماليات"""
        self.calculate_production_totals()
        messagebox.showinfo("تم الحساب", "تم حساب جميع الإجماليات بنجاح")

    def quick_save(self):
        """حفظ سريع"""
        messagebox.showinfo("حفظ سريع", "تم الحفظ السريع بنجاح")

    def clear_current_section(self):
        """مسح القسم الحالي"""
        current = self.selected_section.get()
        result = messagebox.askyesno("تأكيد المسح", f"هل تريد مسح بيانات قسم:\n{current}؟")
        if result:
            # إعادة عرض القسم لمسح البيانات
            self.show_section(current)
            messagebox.showinfo("تم المسح", f"تم مسح بيانات قسم {current}")

    def new_project(self):
        """مشروع جديد"""
        result = messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.")
        if result:
            self.clear_all_data()

    def clear_all_data(self):
        """مسح جميع البيانات"""
        # إعادة عرض القسم الأول
        self.show_section("المعلومات الشخصية + وصف المشروع")
        messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def add_examples(self):
        """إضافة أمثلة للبيانات"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة للبيانات؟")
        if result:
            current = self.selected_section.get()

            if current == "المعلومات الشخصية + وصف المشروع":
                # أمثلة المعلومات الشخصية
                if hasattr(self, 'personal_entries'):
                    self.personal_entries["full_name"].delete(0, tk.END)
                    self.personal_entries["full_name"].insert(0, "أحمد محمد علي")
                    self.personal_entries["age"].delete(0, tk.END)
                    self.personal_entries["age"].insert(0, "30")
                    self.personal_entries["education"].delete(0, tk.END)
                    self.personal_entries["education"].insert(0, "بكالوريوس إدارة أعمال")
                    self.personal_entries["experience"].delete(0, tk.END)
                    self.personal_entries["experience"].insert(0, "5 سنوات في مجال التسويق")
                    self.personal_entries["phone"].delete(0, tk.END)
                    self.personal_entries["phone"].insert(0, "0501234567")
                    self.personal_entries["email"].delete(0, tk.END)
                    self.personal_entries["email"].insert(0, "<EMAIL>")

                    self.project_name_entry.delete(0, tk.END)
                    self.project_name_entry.insert(0, "مشروع متجر إلكتروني للمنتجات المحلية")
                    self.project_desc_text.delete(1.0, tk.END)
                    self.project_desc_text.insert(1.0, "متجر إلكتروني متخصص في بيع المنتجات المحلية والحرفية بجودة عالية وأسعار تنافسية")
                    self.funding_amount_entry.delete(0, tk.END)
                    self.funding_amount_entry.insert(0, "100000")

            elif current == "دراسة السوق والمنافسين":
                if hasattr(self, 'products_entry'):
                    self.products_entry.delete(0, tk.END)
                    self.products_entry.insert(0, "منتجات حرفية، أطعمة محلية، ملابس تراثية")
                    self.services_entry.delete(0, tk.END)
                    self.services_entry.insert(0, "خدمة التوصيل، خدمة العملاء، ضمان الجودة")
                    self.competitor_count.delete(0, tk.END)
                    self.competitor_count.insert(0, "3")
                    self.comp_products_entry.delete(0, tk.END)
                    self.comp_products_entry.insert(0, "متاجر إلكترونية أخرى، محلات تقليدية")

            elif current == "تحليل SWOT":
                if hasattr(self, 'strengths_text'):
                    self.strengths_text.delete(1.0, tk.END)
                    self.strengths_text.insert(1.0, "• خبرة في التسويق\n• شبكة علاقات واسعة\n• فهم عميق للسوق المحلي\n• منتجات عالية الجودة")
                    self.weaknesses_text.delete(1.0, tk.END)
                    self.weaknesses_text.insert(1.0, "• رأس مال محدود\n• نقص في الخبرة التقنية\n• فريق عمل صغير\n• عدم وجود علامة تجارية معروفة")
                    self.opportunities_text.delete(1.0, tk.END)
                    self.opportunities_text.insert(1.0, "• نمو التجارة الإلكترونية\n• دعم حكومي للمشاريع الصغيرة\n• زيادة الاهتمام بالمنتجات المحلية\n• توفر منصات التسويق الرقمي")
                    self.threats_text.delete(1.0, tk.END)
                    self.threats_text.insert(1.0, "• منافسة شديدة\n• تقلبات اقتصادية\n• تغيير في سلوك المستهلكين\n• مشاكل في سلسلة التوريد")

            elif current == "المزيج التسويقي":
                if hasattr(self, 'product_text'):
                    self.product_text.delete(1.0, tk.END)
                    self.product_text.insert(1.0, "منتجات حرفية أصيلة عالية الجودة مع ضمان الأصالة والجودة")
                    self.price_text.delete(1.0, tk.END)
                    self.price_text.insert(1.0, "أسعار تنافسية مع خصومات للعملاء الدائمين وعروض موسمية")
                    self.place_text.delete(1.0, tk.END)
                    self.place_text.insert(1.0, "متجر إلكتروني، وسائل التواصل الاجتماعي، معارض محلية")
                    self.promotion_text.delete(1.0, tk.END)
                    self.promotion_text.insert(1.0, "التسويق عبر وسائل التواصل الاجتماعي، إعلانات مدفوعة، تسويق بالمحتوى")
                    self.people_text.delete(1.0, tk.END)
                    self.people_text.insert(1.0, "فريق متخصص في خدمة العملاء، موردين موثوقين، عملاء مستهدفين من محبي المنتجات المحلية")

            messagebox.showinfo("تم الإضافة", f"تم إضافة الأمثلة لقسم: {current}")

    def open_project(self):
        """فتح مشروع"""
        filename = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                messagebox.showinfo("نجح", "تم فتح المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        filename = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.data = {
                    'timestamp': datetime.now().isoformat(),
                    'current_section': self.selected_section.get()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        messagebox.showinfo("قريباً", "ميزة تصدير PDF ستكون متاحة قريباً")

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📋 دليل استخدام نظام تخطيط المشاريع الشامل

        🎯 كيفية الاستخدام:
        1. اختر القسم من القائمة الجانبية
        2. املأ البيانات المطلوبة بالتفصيل
        3. استخدم الأدوات السريعة للحفظ والمسح
        4. استخدم الأمثلة للتعلم

        📊 الأقسام المتاحة:
        • المعلومات الشخصية ووصف المشروع
        • دراسة السوق والمنافسين
        • تحليل SWOT الشامل
        • المزيج التسويقي الخماسي
        • مستلزمات الإنتاج والمعدات
        • الدراسات المالية المتكاملة
        • الإيرادات والأرباح السنوية

        💡 نصائح:
        • ابدأ بالمعلومات الشخصية
        • أكمل الأقسام بالترتيب
        • استخدم الأمثلة للفهم
        • احفظ عملك بانتظام
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "نظام تخطيط المشاريع الشامل\nالإصدار 1.0\n\nأداة متكاملة لتخطيط وتحليل المشاريع")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

# تشغيل التطبيق
if __name__ == "__main__":
    app = ComprehensiveProjectPlanner()
    app.run()
