#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
المزيج التسويقي المحسّن (7P)
Enhanced Marketing Mix (7P)
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class MarketingMixApp:
    def __init__(self, root):
        self.root = root
        self.root.title("المزيج التسويقي الشامل - خطة العمل")
        self.root.geometry("1200x900")
        self.root.configure(bg="#f5f7fa")
        
        # الخطوط والألوان
        self.label_font = ("Arial", 11, "bold")
        self.entry_font = ("Arial", 10)
        self.title_font = ("Arial", 14, "bold")
        self.box_bg = "#ffffff"
        
        # تخزين البيانات
        self.marketing_data = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        self.create_menu()
        
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        title_frame.pack(fill="x", padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="📢 المزيج التسويقي الشامل (7P)", 
                              font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(expand=True)
        
        # دفتر التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء التبويبات
        self.create_marketing_mix_tab()
        self.create_analysis_tab()
        
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_plan)
        file_menu.add_command(label="فتح", command=self.open_plan)
        file_menu.add_command(label="حفظ", command=self.save_plan)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير تقرير", command=self.export_report)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)
        tools_menu.add_command(label="تحليل المزيج", command=self.analyze_mix)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_marketing_mix_tab(self):
        """إنشاء تبويب المزيج التسويقي الرئيسي"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="📢 المزيج التسويقي (7P)")
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # إرشادات
        instructions_frame = tk.LabelFrame(scrollable_frame, text="📋 إرشادات المزيج التسويقي", 
                                         font=self.label_font, bg="#e8f4fd", relief="groove", bd=2)
        instructions_frame.pack(fill="x", padx=10, pady=10)
        
        instructions_text = """
        📢 المزيج التسويقي (7P) هو مجموعة من الأدوات التسويقية المترابطة:
        
        🛍️ المنتج (Product): ما تقدمه للعملاء من منتجات أو خدمات
        💰 السعر (Price): استراتيجية التسعير والقيمة المقدمة
        📍 المكان (Place): قنوات التوزيع ومكان الوصول للعملاء
        📣 الترويج (Promotion): كيفية التواصل مع العملاء والإعلان
        👥 الناس (People): الفريق والعملاء والعلاقات
        🔧 العمليات (Process): العمليات والإجراءات الداخلية
        🎁 الدليل المادي (Physical Evidence): البيئة المادية والملموسة
        """
        
        tk.Label(instructions_frame, text=instructions_text, font=("Arial", 9), 
                bg="#e8f4fd", justify="right", wraplength=1000).pack(padx=10, pady=5)
        
        # إنشاء أقسام المزيج التسويقي
        self.create_product_section(scrollable_frame)
        self.create_price_section(scrollable_frame)
        self.create_place_section(scrollable_frame)
        self.create_promotion_section(scrollable_frame)
        self.create_people_section(scrollable_frame)
        self.create_process_section(scrollable_frame)
        self.create_physical_evidence_section(scrollable_frame)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=20)
        
        tk.Button(buttons_frame, text="💾 حفظ المزيج", command=self.save_marketing_data,
                 bg="#3498db", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)
        
        tk.Button(buttons_frame, text="🔄 مسح البيانات", command=self.clear_marketing_data,
                 bg="#e74c3c", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)
        
        tk.Button(buttons_frame, text="📊 تحليل المزيج", command=self.analyze_mix,
                 bg="#f39c12", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)
        
        tk.Button(buttons_frame, text="💡 أمثلة", command=self.add_examples,
                 bg="#9b59b6", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        return tab
    
    def create_product_section(self, parent):
        """إنشاء قسم المنتج"""
        product_frame = tk.LabelFrame(parent, text="🛍️ المنتج (Product)", 
                                    font=self.title_font, bg="#e8f5e8", relief="groove", bd=2)
        product_frame.pack(fill="x", padx=10, pady=10)
        
        # وصف المنتج
        tk.Label(product_frame, text="وصف المنتج/الخدمة:", font=self.label_font, bg="#e8f5e8").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        self.product_description = tk.Text(product_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.product_description.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
        
        # المزايا التنافسية
        tk.Label(product_frame, text="المزايا التنافسية:", font=self.label_font, bg="#e8f5e8").grid(
            row=1, column=0, sticky="nw", padx=10, pady=5)
        self.competitive_advantages = tk.Text(product_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.competitive_advantages.grid(row=1, column=1, padx=10, pady=5, sticky="ew")
        
        # جودة المنتج
        tk.Label(product_frame, text="مستوى الجودة:", font=self.label_font, bg="#e8f5e8").grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        
        self.quality_level = tk.StringVar(value="عالي")
        quality_frame = tk.Frame(product_frame, bg="#e8f5e8")
        quality_frame.grid(row=2, column=1, sticky="w", padx=10, pady=5)
        
        for quality in ["ممتاز", "عالي", "متوسط", "أساسي"]:
            tk.Radiobutton(quality_frame, text=quality, variable=self.quality_level, 
                          value=quality, bg="#e8f5e8").pack(side="left", padx=5)
        
        # دورة حياة المنتج
        tk.Label(product_frame, text="مرحلة دورة الحياة:", font=self.label_font, bg="#e8f5e8").grid(
            row=3, column=0, sticky="e", padx=10, pady=5)
        
        self.lifecycle_stage = tk.StringVar(value="تطوير")
        lifecycle_frame = tk.Frame(product_frame, bg="#e8f5e8")
        lifecycle_frame.grid(row=3, column=1, sticky="w", padx=10, pady=5)
        
        for stage in ["تطوير", "إطلاق", "نمو", "نضج", "انحدار"]:
            tk.Radiobutton(lifecycle_frame, text=stage, variable=self.lifecycle_stage, 
                          value=stage, bg="#e8f5e8").pack(side="left", padx=5)
        
        product_frame.grid_columnconfigure(1, weight=1)
    
    def create_price_section(self, parent):
        """إنشاء قسم السعر"""
        price_frame = tk.LabelFrame(parent, text="💰 السعر (Price)", 
                                  font=self.title_font, bg="#ffe8e8", relief="groove", bd=2)
        price_frame.pack(fill="x", padx=10, pady=10)
        
        # استراتيجية التسعير
        tk.Label(price_frame, text="استراتيجية التسعير:", font=self.label_font, bg="#ffe8e8").grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        
        self.pricing_strategy = tk.StringVar(value="تنافسي")
        strategy_frame = tk.Frame(price_frame, bg="#ffe8e8")
        strategy_frame.grid(row=0, column=1, sticky="w", padx=10, pady=5)
        
        strategies = ["اختراق السوق", "كشط السوق", "تنافسي", "قيمة مضافة", "تكلفة زائد ربح"]
        for strategy in strategies:
            tk.Radiobutton(strategy_frame, text=strategy, variable=self.pricing_strategy, 
                          value=strategy, bg="#ffe8e8").pack(anchor="w")
        
        # السعر المقترح
        tk.Label(price_frame, text="السعر المقترح:", font=self.label_font, bg="#ffe8e8").grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.suggested_price = tk.Entry(price_frame, width=20, font=self.entry_font)
        self.suggested_price.grid(row=1, column=1, sticky="w", padx=10, pady=5)
        
        # هامش الربح
        tk.Label(price_frame, text="هامش الربح المستهدف (%):", font=self.label_font, bg="#ffe8e8").grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        self.profit_margin = tk.Entry(price_frame, width=20, font=self.entry_font)
        self.profit_margin.grid(row=2, column=1, sticky="w", padx=10, pady=5)
        
        # مرونة السعر
        tk.Label(price_frame, text="مرونة التسعير:", font=self.label_font, bg="#ffe8e8").grid(
            row=3, column=0, sticky="nw", padx=10, pady=5)
        self.price_flexibility = tk.Text(price_frame, height=2, width=60, font=self.entry_font, wrap=tk.WORD)
        self.price_flexibility.grid(row=3, column=1, padx=10, pady=5, sticky="ew")
        
        price_frame.grid_columnconfigure(1, weight=1)
    
    def create_place_section(self, parent):
        """إنشاء قسم المكان/التوزيع"""
        place_frame = tk.LabelFrame(parent, text="📍 المكان/التوزيع (Place)", 
                                  font=self.title_font, bg="#e8f0ff", relief="groove", bd=2)
        place_frame.pack(fill="x", padx=10, pady=10)
        
        # قنوات التوزيع
        tk.Label(place_frame, text="قنوات التوزيع:", font=self.label_font, bg="#e8f0ff").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        
        channels_frame = tk.Frame(place_frame, bg="#e8f0ff")
        channels_frame.grid(row=0, column=1, sticky="w", padx=10, pady=5)
        
        self.distribution_channels = {}
        channels = ["متجر فعلي", "متجر إلكتروني", "وسطاء/موزعين", "بيع مباشر", 
                   "تطبيقات التوصيل", "معارض", "أسواق شعبية"]
        
        for i, channel in enumerate(channels):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(channels_frame, text=channel, variable=var, bg="#e8f0ff")
            cb.grid(row=i//3, column=i%3, sticky="w", padx=5, pady=2)
            self.distribution_channels[channel] = var
        
        # الموقع الجغرافي
        tk.Label(place_frame, text="الموقع الجغرافي:", font=self.label_font, bg="#e8f0ff").grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.geographic_location = tk.Entry(place_frame, width=50, font=self.entry_font)
        self.geographic_location.grid(row=1, column=1, sticky="w", padx=10, pady=5)
        
        # نطاق التغطية
        tk.Label(place_frame, text="نطاق التغطية:", font=self.label_font, bg="#e8f0ff").grid(
            row=2, column=0, sticky="e", padx=10, pady=5)
        
        self.coverage_area = tk.StringVar(value="محلي")
        coverage_frame = tk.Frame(place_frame, bg="#e8f0ff")
        coverage_frame.grid(row=2, column=1, sticky="w", padx=10, pady=5)
        
        for area in ["محلي", "إقليمي", "وطني", "دولي"]:
            tk.Radiobutton(coverage_frame, text=area, variable=self.coverage_area, 
                          value=area, bg="#e8f0ff").pack(side="left", padx=5)
        
        place_frame.grid_columnconfigure(1, weight=1)

    def create_promotion_section(self, parent):
        """إنشاء قسم الترويج"""
        promotion_frame = tk.LabelFrame(parent, text="📣 الترويج (Promotion)",
                                      font=self.title_font, bg="#fff2e8", relief="groove", bd=2)
        promotion_frame.pack(fill="x", padx=10, pady=10)

        # وسائل الترويج
        tk.Label(promotion_frame, text="وسائل الترويج:", font=self.label_font, bg="#fff2e8").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)

        promo_methods_frame = tk.Frame(promotion_frame, bg="#fff2e8")
        promo_methods_frame.grid(row=0, column=1, sticky="w", padx=10, pady=5)

        self.promotion_methods = {}
        methods = ["وسائل التواصل الاجتماعي", "الإعلان المدفوع", "التسويق بالمحتوى",
                  "العلاقات العامة", "الكلمة المنطوقة", "العروض والخصومات",
                  "المعارض والفعاليات", "التسويق المباشر"]

        for i, method in enumerate(methods):
            var = tk.BooleanVar()
            cb = tk.Checkbutton(promo_methods_frame, text=method, variable=var, bg="#fff2e8")
            cb.grid(row=i//3, column=i%3, sticky="w", padx=5, pady=2)
            self.promotion_methods[method] = var

        # الميزانية الشهرية
        tk.Label(promotion_frame, text="الميزانية الشهرية للترويج:", font=self.label_font, bg="#fff2e8").grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.promotion_budget = tk.Entry(promotion_frame, width=20, font=self.entry_font)
        self.promotion_budget.grid(row=1, column=1, sticky="w", padx=10, pady=5)

        # الرسالة التسويقية
        tk.Label(promotion_frame, text="الرسالة التسويقية الرئيسية:", font=self.label_font, bg="#fff2e8").grid(
            row=2, column=0, sticky="nw", padx=10, pady=5)
        self.marketing_message = tk.Text(promotion_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.marketing_message.grid(row=2, column=1, padx=10, pady=5, sticky="ew")

        promotion_frame.grid_columnconfigure(1, weight=1)

    def create_people_section(self, parent):
        """إنشاء قسم الناس"""
        people_frame = tk.LabelFrame(parent, text="👥 الناس (People)",
                                   font=self.title_font, bg="#f0f9ff", relief="groove", bd=2)
        people_frame.pack(fill="x", padx=10, pady=10)

        # الفريق
        tk.Label(people_frame, text="وصف الفريق:", font=self.label_font, bg="#f0f9ff").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        self.team_description = tk.Text(people_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.team_description.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        # العملاء المستهدفين
        tk.Label(people_frame, text="العملاء المستهدفين:", font=self.label_font, bg="#f0f9ff").grid(
            row=1, column=0, sticky="nw", padx=10, pady=5)
        self.target_customers = tk.Text(people_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.target_customers.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        # خدمة العملاء
        tk.Label(people_frame, text="استراتيجية خدمة العملاء:", font=self.label_font, bg="#f0f9ff").grid(
            row=2, column=0, sticky="nw", padx=10, pady=5)
        self.customer_service = tk.Text(people_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.customer_service.grid(row=2, column=1, padx=10, pady=5, sticky="ew")

        people_frame.grid_columnconfigure(1, weight=1)

    def create_process_section(self, parent):
        """إنشاء قسم العمليات"""
        process_frame = tk.LabelFrame(parent, text="🔧 العمليات (Process)",
                                    font=self.title_font, bg="#f5f0ff", relief="groove", bd=2)
        process_frame.pack(fill="x", padx=10, pady=10)

        # عملية البيع
        tk.Label(process_frame, text="عملية البيع:", font=self.label_font, bg="#f5f0ff").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        self.sales_process = tk.Text(process_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.sales_process.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        # عملية التسليم
        tk.Label(process_frame, text="عملية التسليم:", font=self.label_font, bg="#f5f0ff").grid(
            row=1, column=0, sticky="nw", padx=10, pady=5)
        self.delivery_process = tk.Text(process_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.delivery_process.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        # ضمان الجودة
        tk.Label(process_frame, text="ضمان الجودة:", font=self.label_font, bg="#f5f0ff").grid(
            row=2, column=0, sticky="nw", padx=10, pady=5)
        self.quality_assurance = tk.Text(process_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.quality_assurance.grid(row=2, column=1, padx=10, pady=5, sticky="ew")

        process_frame.grid_columnconfigure(1, weight=1)

    def create_physical_evidence_section(self, parent):
        """إنشاء قسم الدليل المادي"""
        physical_frame = tk.LabelFrame(parent, text="🎁 الدليل المادي (Physical Evidence)",
                                     font=self.title_font, bg="#fff5f0", relief="groove", bd=2)
        physical_frame.pack(fill="x", padx=10, pady=10)

        # البيئة المادية
        tk.Label(physical_frame, text="البيئة المادية:", font=self.label_font, bg="#fff5f0").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        self.physical_environment = tk.Text(physical_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.physical_environment.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        # التصميم والعلامة التجارية
        tk.Label(physical_frame, text="التصميم والعلامة التجارية:", font=self.label_font, bg="#fff5f0").grid(
            row=1, column=0, sticky="nw", padx=10, pady=5)
        self.branding_design = tk.Text(physical_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.branding_design.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        # المواد التسويقية
        tk.Label(physical_frame, text="المواد التسويقية:", font=self.label_font, bg="#fff5f0").grid(
            row=2, column=0, sticky="nw", padx=10, pady=5)
        self.marketing_materials = tk.Text(physical_frame, height=3, width=60, font=self.entry_font, wrap=tk.WORD)
        self.marketing_materials.grid(row=2, column=1, padx=10, pady=5, sticky="ew")

        physical_frame.grid_columnconfigure(1, weight=1)

    def create_analysis_tab(self):
        """إنشاء تبويب التحليل"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="📊 تحليل المزيج")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تحليل التماسك
        coherence_frame = tk.LabelFrame(scrollable_frame, text="🔗 تحليل تماسك المزيج التسويقي",
                                      font=self.title_font, bg="#e8f4fd", relief="groove", bd=2)
        coherence_frame.pack(fill="x", padx=10, pady=10)

        self.coherence_analysis = tk.Text(coherence_frame, height=6, font=self.entry_font,
                                        wrap=tk.WORD, bg="white")
        self.coherence_analysis.pack(fill="x", padx=10, pady=10)

        # نقاط القوة والضعف
        strengths_weaknesses_frame = tk.LabelFrame(scrollable_frame, text="⚖️ نقاط القوة والضعف في المزيج",
                                                 font=self.title_font, bg="#fff2e8", relief="groove", bd=2)
        strengths_weaknesses_frame.pack(fill="x", padx=10, pady=10)

        # نقاط القوة
        tk.Label(strengths_weaknesses_frame, text="نقاط القوة:", font=self.label_font, bg="#fff2e8").grid(
            row=0, column=0, sticky="nw", padx=10, pady=5)
        self.mix_strengths = tk.Text(strengths_weaknesses_frame, height=4, width=50, font=self.entry_font, wrap=tk.WORD)
        self.mix_strengths.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        # نقاط الضعف
        tk.Label(strengths_weaknesses_frame, text="نقاط الضعف:", font=self.label_font, bg="#fff2e8").grid(
            row=1, column=0, sticky="nw", padx=10, pady=5)
        self.mix_weaknesses = tk.Text(strengths_weaknesses_frame, height=4, width=50, font=self.entry_font, wrap=tk.WORD)
        self.mix_weaknesses.grid(row=1, column=1, padx=10, pady=5, sticky="ew")

        strengths_weaknesses_frame.grid_columnconfigure(1, weight=1)

        # التوصيات
        recommendations_frame = tk.LabelFrame(scrollable_frame, text="💡 التوصيات والتحسينات",
                                            font=self.title_font, bg="#f0f9ff", relief="groove", bd=2)
        recommendations_frame.pack(fill="x", padx=10, pady=10)

        self.recommendations = tk.Text(recommendations_frame, height=6, font=self.entry_font,
                                     wrap=tk.WORD, bg="white")
        self.recommendations.pack(fill="x", padx=10, pady=10)

        # أزرار التحليل
        analysis_buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        analysis_buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(analysis_buttons_frame, text="🔄 تحليل تلقائي", command=self.auto_analyze,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(analysis_buttons_frame, text="💡 إنشاء توصيات", command=self.generate_recommendations,
                 bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(analysis_buttons_frame, text="🔄 مسح التحليل", command=self.clear_analysis,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def save_marketing_data(self):
        """حفظ بيانات المزيج التسويقي"""
        self.collect_marketing_data()
        messagebox.showinfo("تم الحفظ", "تم حفظ بيانات المزيج التسويقي بنجاح")

    def collect_marketing_data(self):
        """جمع بيانات المزيج التسويقي من الواجهة"""
        self.marketing_data = {
            'product': {
                'description': self.product_description.get(1.0, tk.END).strip(),
                'competitive_advantages': self.competitive_advantages.get(1.0, tk.END).strip(),
                'quality_level': self.quality_level.get(),
                'lifecycle_stage': self.lifecycle_stage.get()
            },
            'price': {
                'strategy': self.pricing_strategy.get(),
                'suggested_price': self.suggested_price.get(),
                'profit_margin': self.profit_margin.get(),
                'flexibility': self.price_flexibility.get(1.0, tk.END).strip()
            },
            'place': {
                'channels': {k: v.get() for k, v in self.distribution_channels.items()},
                'location': self.geographic_location.get(),
                'coverage': self.coverage_area.get()
            },
            'promotion': {
                'methods': {k: v.get() for k, v in self.promotion_methods.items()},
                'budget': self.promotion_budget.get(),
                'message': self.marketing_message.get(1.0, tk.END).strip()
            },
            'people': {
                'team': self.team_description.get(1.0, tk.END).strip(),
                'customers': self.target_customers.get(1.0, tk.END).strip(),
                'service': self.customer_service.get(1.0, tk.END).strip()
            },
            'process': {
                'sales': self.sales_process.get(1.0, tk.END).strip(),
                'delivery': self.delivery_process.get(1.0, tk.END).strip(),
                'quality': self.quality_assurance.get(1.0, tk.END).strip()
            },
            'physical_evidence': {
                'environment': self.physical_environment.get(1.0, tk.END).strip(),
                'branding': self.branding_design.get(1.0, tk.END).strip(),
                'materials': self.marketing_materials.get(1.0, tk.END).strip()
            }
        }

    def clear_marketing_data(self):
        """مسح بيانات المزيج التسويقي"""
        result = messagebox.askyesno("تأكيد المسح", "هل تريد مسح جميع بيانات المزيج التسويقي؟")
        if result:
            # مسح المنتج
            self.product_description.delete(1.0, tk.END)
            self.competitive_advantages.delete(1.0, tk.END)
            self.quality_level.set("عالي")
            self.lifecycle_stage.set("تطوير")

            # مسح السعر
            self.pricing_strategy.set("تنافسي")
            self.suggested_price.delete(0, tk.END)
            self.profit_margin.delete(0, tk.END)
            self.price_flexibility.delete(1.0, tk.END)

            # مسح المكان
            for var in self.distribution_channels.values():
                var.set(False)
            self.geographic_location.delete(0, tk.END)
            self.coverage_area.set("محلي")

            # مسح الترويج
            for var in self.promotion_methods.values():
                var.set(False)
            self.promotion_budget.delete(0, tk.END)
            self.marketing_message.delete(1.0, tk.END)

            # مسح الناس
            self.team_description.delete(1.0, tk.END)
            self.target_customers.delete(1.0, tk.END)
            self.customer_service.delete(1.0, tk.END)

            # مسح العمليات
            self.sales_process.delete(1.0, tk.END)
            self.delivery_process.delete(1.0, tk.END)
            self.quality_assurance.delete(1.0, tk.END)

            # مسح الدليل المادي
            self.physical_environment.delete(1.0, tk.END)
            self.branding_design.delete(1.0, tk.END)
            self.marketing_materials.delete(1.0, tk.END)

            messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def analyze_mix(self):
        """تحليل المزيج التسويقي"""
        self.auto_analyze()
        self.generate_recommendations()
        # التبديل إلى تبويب التحليل
        self.notebook.select(1)

    def auto_analyze(self):
        """تحليل تلقائي للمزيج التسويقي"""
        self.collect_marketing_data()

        analysis = []

        # تحليل التماسك
        analysis.append("🔍 تحليل تماسك المزيج التسويقي:\n")

        # فحص اكتمال البيانات
        completed_sections = 0
        total_sections = 7

        if self.marketing_data['product']['description']:
            completed_sections += 1
            analysis.append("✅ قسم المنتج مكتمل")
        else:
            analysis.append("❌ قسم المنتج يحتاج لمزيد من التفاصيل")

        if self.marketing_data['price']['suggested_price']:
            completed_sections += 1
            analysis.append("✅ قسم السعر مكتمل")
        else:
            analysis.append("❌ قسم السعر يحتاج لتحديد السعر")

        if any(self.marketing_data['place']['channels'].values()):
            completed_sections += 1
            analysis.append("✅ قسم المكان مكتمل")
        else:
            analysis.append("❌ قسم المكان يحتاج لتحديد قنوات التوزيع")

        if any(self.marketing_data['promotion']['methods'].values()):
            completed_sections += 1
            analysis.append("✅ قسم الترويج مكتمل")
        else:
            analysis.append("❌ قسم الترويج يحتاج لتحديد وسائل الترويج")

        if self.marketing_data['people']['team']:
            completed_sections += 1
            analysis.append("✅ قسم الناس مكتمل")
        else:
            analysis.append("❌ قسم الناس يحتاج لوصف الفريق")

        if self.marketing_data['process']['sales']:
            completed_sections += 1
            analysis.append("✅ قسم العمليات مكتمل")
        else:
            analysis.append("❌ قسم العمليات يحتاج لوصف العمليات")

        if self.marketing_data['physical_evidence']['environment']:
            completed_sections += 1
            analysis.append("✅ قسم الدليل المادي مكتمل")
        else:
            analysis.append("❌ قسم الدليل المادي يحتاج لمزيد من التفاصيل")

        completion_rate = (completed_sections / total_sections) * 100
        analysis.append(f"\n📊 معدل اكتمال المزيج التسويقي: {completion_rate:.1f}%")

        if completion_rate >= 80:
            analysis.append("🎉 المزيج التسويقي متماسك وشامل!")
        elif completion_rate >= 60:
            analysis.append("⚠️ المزيج التسويقي جيد لكن يحتاج لبعض التحسينات")
        else:
            analysis.append("🔧 المزيج التسويقي يحتاج لمزيد من العمل والتطوير")

        # عرض التحليل
        self.coherence_analysis.delete(1.0, tk.END)
        self.coherence_analysis.insert(1.0, "\n".join(analysis))

    def generate_recommendations(self):
        """إنشاء توصيات للتحسين"""
        self.collect_marketing_data()

        recommendations = []

        # توصيات المنتج
        if not self.marketing_data['product']['description']:
            recommendations.append("🛍️ أضف وصفاً مفصلاً للمنتج/الخدمة")

        if self.marketing_data['product']['quality_level'] == "أساسي":
            recommendations.append("📈 فكر في تحسين جودة المنتج للحصول على ميزة تنافسية")

        # توصيات السعر
        if not self.marketing_data['price']['suggested_price']:
            recommendations.append("💰 حدد سعراً واضحاً للمنتج")

        if not self.marketing_data['price']['profit_margin']:
            recommendations.append("📊 احسب هامش الربح المستهدف")

        # توصيات المكان
        selected_channels = sum(self.marketing_data['place']['channels'].values())
        if selected_channels == 0:
            recommendations.append("📍 اختر قنوات توزيع مناسبة")
        elif selected_channels == 1:
            recommendations.append("🔄 فكر في تنويع قنوات التوزيع لزيادة الوصول")

        # توصيات الترويج
        selected_methods = sum(self.marketing_data['promotion']['methods'].values())
        if selected_methods == 0:
            recommendations.append("📣 اختر وسائل ترويج مناسبة")
        elif selected_methods >= 5:
            recommendations.append("🎯 ركز على وسائل الترويج الأكثر فعالية")

        if not self.marketing_data['promotion']['budget']:
            recommendations.append("💵 حدد ميزانية واضحة للترويج")

        # توصيات عامة
        if not recommendations:
            recommendations.append("✅ المزيج التسويقي متكامل ومتوازن!")
            recommendations.append("🚀 يمكنك الآن تنفيذ الخطة التسويقية")
        else:
            recommendations.append("💡 بعد تطبيق هذه التوصيات، ستحصل على مزيج تسويقي أقوى")

        # عرض التوصيات
        self.recommendations.delete(1.0, tk.END)
        self.recommendations.insert(1.0, "\n".join(recommendations))

        # تحديث نقاط القوة والضعف
        self.update_strengths_weaknesses()

    def update_strengths_weaknesses(self):
        """تحديث نقاط القوة والضعف"""
        strengths = []
        weaknesses = []

        # تحليل المنتج
        if self.marketing_data['product']['quality_level'] in ["ممتاز", "عالي"]:
            strengths.append("• جودة منتج عالية")
        elif self.marketing_data['product']['quality_level'] == "أساسي":
            weaknesses.append("• جودة منتج أساسية")

        # تحليل السعر
        if self.marketing_data['price']['strategy'] == "قيمة مضافة":
            strengths.append("• استراتيجية تسعير قيمة مضافة")
        elif self.marketing_data['price']['strategy'] == "اختراق السوق":
            strengths.append("• استراتيجية تسعير تنافسية")

        # تحليل المكان
        selected_channels = sum(self.marketing_data['place']['channels'].values())
        if selected_channels >= 3:
            strengths.append("• تنوع في قنوات التوزيع")
        elif selected_channels <= 1:
            weaknesses.append("• قنوات توزيع محدودة")

        # تحليل الترويج
        selected_methods = sum(self.marketing_data['promotion']['methods'].values())
        if selected_methods >= 4:
            strengths.append("• تنوع في وسائل الترويج")
        elif selected_methods <= 2:
            weaknesses.append("• وسائل ترويج محدودة")

        # عرض النتائج
        self.mix_strengths.delete(1.0, tk.END)
        if strengths:
            self.mix_strengths.insert(1.0, "\n".join(strengths))
        else:
            self.mix_strengths.insert(1.0, "لم يتم تحديد نقاط قوة واضحة بعد")

        self.mix_weaknesses.delete(1.0, tk.END)
        if weaknesses:
            self.mix_weaknesses.insert(1.0, "\n".join(weaknesses))
        else:
            self.mix_weaknesses.insert(1.0, "لا توجد نقاط ضعف واضحة")

    def clear_analysis(self):
        """مسح التحليل"""
        self.coherence_analysis.delete(1.0, tk.END)
        self.mix_strengths.delete(1.0, tk.END)
        self.mix_weaknesses.delete(1.0, tk.END)
        self.recommendations.delete(1.0, tk.END)

    def add_examples(self):
        """إضافة أمثلة للمزيج التسويقي"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة للمزيج التسويقي؟\nسيتم مسح البيانات الحالية.")
        if result:
            # مسح البيانات الحالية
            self.clear_marketing_data()

            # أمثلة المنتج
            self.product_description.insert(1.0, "منتج تقني مبتكر يحل مشكلة حقيقية في السوق")
            self.competitive_advantages.insert(1.0, "تقنية متقدمة، سهولة الاستخدام، دعم فني ممتاز")
            self.quality_level.set("عالي")
            self.lifecycle_stage.set("إطلاق")

            # أمثلة السعر
            self.pricing_strategy.set("قيمة مضافة")
            self.suggested_price.insert(0, "299")
            self.profit_margin.insert(0, "40")
            self.price_flexibility.insert(1.0, "إمكانية تقديم خصومات للعملاء الجدد وخصومات الكمية")

            # أمثلة المكان
            self.distribution_channels["متجر إلكتروني"].set(True)
            self.distribution_channels["بيع مباشر"].set(True)
            self.geographic_location.insert(0, "المدن الرئيسية")
            self.coverage_area.set("وطني")

            # أمثلة الترويج
            self.promotion_methods["وسائل التواصل الاجتماعي"].set(True)
            self.promotion_methods["التسويق بالمحتوى"].set(True)
            self.promotion_methods["العروض والخصومات"].set(True)
            self.promotion_budget.insert(0, "5000")
            self.marketing_message.insert(1.0, "الحل الأمثل لاحتياجاتك التقنية - جودة عالية وسعر منافس")

            # أمثلة الناس
            self.team_description.insert(1.0, "فريق متخصص من المهندسين والمسوقين ذوي خبرة عالية")
            self.target_customers.insert(1.0, "الشركات الصغيرة والمتوسطة، المهنيين المستقلين")
            self.customer_service.insert(1.0, "دعم فني 24/7، ضمان شامل، تدريب مجاني")

            # أمثلة العمليات
            self.sales_process.insert(1.0, "استشارة مجانية، عرض مخصص، تجربة مجانية، إتمام البيع")
            self.delivery_process.insert(1.0, "تسليم سريع خلال 48 ساعة، تركيب وتدريب")
            self.quality_assurance.insert(1.0, "فحص شامل قبل التسليم، متابعة دورية، ضمان الجودة")

            # أمثلة الدليل المادي
            self.physical_environment.insert(1.0, "مكتب أنيق ومعاصر، معرض للمنتجات")
            self.branding_design.insert(1.0, "شعار احترافي، ألوان متناسقة، تصميم عصري")
            self.marketing_materials.insert(1.0, "كتيبات تعريفية، موقع إلكتروني، بطاقات عمل")

    def new_plan(self):
        """خطة جديدة"""
        self.clear_all_data()

    def open_plan(self):
        """فتح خطة محفوظة"""
        filename = filedialog.askopenfilename(
            title="فتح خطة المزيج التسويقي",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.load_data(data)
                messagebox.showinfo("نجح", "تم فتح الخطة بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_plan(self):
        """حفظ الخطة"""
        filename = filedialog.asksaveasfilename(
            title="حفظ خطة المزيج التسويقي",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.collect_marketing_data()
                data = {
                    'marketing_data': self.marketing_data,
                    'timestamp': datetime.now().isoformat()
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ الخطة بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def load_data(self, data):
        """تحميل البيانات"""
        if 'marketing_data' in data:
            # تحميل البيانات هنا
            pass

    def export_report(self):
        """تصدير تقرير"""
        messagebox.showinfo("قريباً", "ميزة تصدير التقرير ستكون متاحة قريباً")

    def clear_all_data(self):
        """مسح جميع البيانات"""
        self.clear_marketing_data()
        self.clear_analysis()

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📢 دليل استخدام تطبيق المزيج التسويقي

        🎯 ما هو المزيج التسويقي؟
        المزيج التسويقي (7P) يشمل:
        • المنتج (Product)
        • السعر (Price)
        • المكان (Place)
        • الترويج (Promotion)
        • الناس (People)
        • العمليات (Process)
        • الدليل المادي (Physical Evidence)

        📝 كيفية الاستخدام:
        1. املأ كل قسم بالتفاصيل المناسبة
        2. استخدم التحليل التلقائي
        3. راجع التوصيات المقترحة
        4. طبق التحسينات المطلوبة

        💾 الحفظ والتحميل:
        • احفظ خطتك في ملف JSON
        • حمّل خطط سابقة
        • صدّر تقارير (قريباً)
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "تطبيق المزيج التسويقي الشامل\nالإصدار 1.0\n\nأداة متقدمة لتطوير المزيج التسويقي (7P)")

if __name__ == "__main__":
    root = tk.Tk()
    app = MarketingMixApp(root)
    root.mainloop()
