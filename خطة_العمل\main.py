#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تخطيط المشاريع الشامل - الملف الرئيسي
Comprehensive Project Planning System - Main File

المطور: نظام ذكي لتخطيط المشاريع
التاريخ: 2024
الإصدار: 2.0 Professional
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'sections'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

# استيراد الأقسام
try:
    from sections.section1_info_summary import PersonalInfoSection
    from sections.section2_market import MarketAnalysisSection
    from sections.section3_swot import SwotAnalysisSection
    from sections.section4_marketing_mix import MarketingMixSection
    from utils.save_load import DataManager

    # استيراد الأقسام المتبقية مع معالجة الأخطاء
    try:
        from sections.section5_requirements import RequirementsSection
    except ImportError:
        RequirementsSection = None

    try:
        from sections.section6_startup_costs import StartupCostsSection
    except ImportError:
        StartupCostsSection = None

    try:
        from sections.section7_operating_costs import OperatingCostsSection
    except ImportError:
        OperatingCostsSection = None

    try:
        from sections.section8_summary import FinancialSummarySection
    except ImportError:
        FinancialSummarySection = None

    try:
        from sections.section9_annual_revenue import AnnualRevenueSection
    except ImportError:
        AnnualRevenueSection = None

    try:
        from sections.section10_profit_loss import ProfitLossSection
    except ImportError:
        ProfitLossSection = None

except ImportError as e:
    print(f"خطأ في استيراد الملفات: {e}")
    # إنشاء classes بديلة
    class PersonalInfoSection:
        def __init__(self, parent, colors, fonts): pass
    class MarketAnalysisSection:
        def __init__(self, parent, colors, fonts): pass
    class SwotAnalysisSection:
        def __init__(self, parent, colors, fonts): pass
    class MarketingMixSection:
        def __init__(self, parent, colors, fonts): pass
    class DataManager:
        def save_project(self, data): pass
        def load_project(self): return None

class BusinessPlanApp:
    """التطبيق الرئيسي لنظام تخطيط المشاريع"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.setup_colors_fonts()
        self.data_manager = DataManager()
        self.sections_data = {}
        self.current_section = None
        
        # إنشاء الواجهة
        self.create_interface()
        
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🏢 نظام تخطيط المشاريع الشامل - Comprehensive Business Plan System")
        self.root.geometry("1600x1000")
        self.root.state('zoomed')  # ملء الشاشة
        self.root.configure(bg="#f5f7fa")
        
        # أيقونة التطبيق (اختيارية)
        try:
            self.root.iconbitmap("assets/logo.ico")
        except:
            pass
            
    def setup_colors_fonts(self):
        """إعداد الألوان والخطوط"""
        # ألوان احترافية هادئة
        self.colors = {
            'primary': '#2c5282',
            'secondary': '#4a5568', 
            'accent': '#d69e2e',
            'success': '#38a169',
            'warning': '#dd6b20',
            'danger': '#e53e3e',
            'info': '#3182ce',
            'light': '#f7fafc',
            'dark': '#1a202c',
            'white': '#ffffff',
            'gray_light': '#f1f5f9',
            'gray_medium': '#e2e8f0',
            'text_primary': '#2d3748',
            'text_secondary': '#4a5568'
        }
        
        # خطوط تدعم العربية
        self.fonts = {
            'title': ('Tahoma', 18, 'bold'),
            'subtitle': ('Tahoma', 14, 'bold'),
            'normal': ('Tahoma', 12, 'normal'),
            'small': ('Tahoma', 10, 'normal'),
            'button': ('Tahoma', 11, 'bold'),
            'arabic': ('Arial', 12, 'normal')
        }
        
    def create_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # === الحاوي الرئيسي ===
        main_container = tk.Frame(self.root, bg=self.colors['light'])
        main_container.pack(fill="both", expand=True)
        
        # === رأس التطبيق ===
        self.create_header(main_container)
        
        # === الجسم الرئيسي ===
        body_container = tk.Frame(main_container, bg=self.colors['light'])
        body_container.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # === القائمة الجانبية ===
        self.create_sidebar(body_container)
        
        # === منطقة المحتوى ===
        self.create_content_area(body_container)
        
        # === شريط الحالة ===
        self.create_status_bar(main_container)
        
    def create_header(self, parent):
        """إنشاء رأس التطبيق"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        # محتوى الرأس
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(expand=True, fill="both", padx=30)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_content, 
                              text="🏢 نظام تخطيط المشاريع الشامل",
                              font=self.fonts['title'], 
                              bg=self.colors['primary'], 
                              fg=self.colors['white'],
                              anchor="e", justify="right")
        title_label.pack(anchor="e", fill="x", pady=(15, 5))
        
        # العنوان الفرعي
        subtitle_label = tk.Label(header_content,
                                 text="نظام متكامل لإعداد خطط الأعمال الاحترافية مع دعم RTL كامل",
                                 font=self.fonts['normal'],
                                 bg=self.colors['primary'],
                                 fg=self.colors['gray_medium'],
                                 anchor="e", justify="right")
        subtitle_label.pack(anchor="e", fill="x", pady=(0, 15))
        
    def create_sidebar(self, parent):
        """إنشاء القائمة الجانبية"""
        # إطار القائمة الجانبية
        self.sidebar = tk.Frame(parent, bg=self.colors['white'], width=350, relief="solid", bd=1)
        self.sidebar.pack(side="right", fill="y", padx=(0, 20))
        self.sidebar.pack_propagate(False)
        
        # رأس القائمة
        sidebar_header = tk.Frame(self.sidebar, bg=self.colors['secondary'], height=60)
        sidebar_header.pack(fill="x")
        sidebar_header.pack_propagate(False)
        
        tk.Label(sidebar_header, text="📋 أقسام خطة العمل",
                font=self.fonts['subtitle'], bg=self.colors['secondary'], 
                fg=self.colors['accent'], anchor="e").pack(expand=True, padx=20)
        
        # خط ذهبي
        tk.Frame(self.sidebar, bg=self.colors['accent'], height=3).pack(fill="x")
        
        # قائمة الأقسام
        self.sections_list = [
            ("👤", "المعلومات الشخصية", "section1"),
            ("📊", "دراسة السوق", "section2"), 
            ("⚖️", "تحليل SWOT", "section3"),
            ("🎯", "المزيج التسويقي", "section4"),
            ("🔧", "مستلزمات الإنتاج", "section5"),
            ("💰", "التكاليف التأسيسية", "section6"),
            ("📈", "التكاليف التشغيلية", "section7"),
            ("📋", "الملخص المالي", "section8"),
            ("💵", "الإيرادات السنوية", "section9"),
            ("📊", "الربح والخسارة", "section10")
        ]
        
        self.selected_section = tk.StringVar(value="section1")
        self.section_buttons = {}
        
        # إنشاء أزرار الأقسام
        for icon, name, section_id in self.sections_list:
            self.create_section_button(self.sidebar, icon, name, section_id)
            
        # أدوات سريعة
        self.create_quick_tools(self.sidebar)
        
    def create_section_button(self, parent, icon, name, section_id):
        """إنشاء زر قسم"""
        btn_container = tk.Frame(parent, bg=self.colors['white'])
        btn_container.pack(fill="x", padx=15, pady=6)
        
        btn_frame = tk.Frame(btn_container, bg=self.colors['gray_light'], relief="solid", bd=1)
        btn_frame.pack(fill="x")
        
        btn = tk.Radiobutton(btn_frame, 
                            text=f"{name} {icon}",
                            variable=self.selected_section, 
                            value=section_id,
                            indicatoron=0,
                            command=lambda: self.show_section(section_id),
                            font=self.fonts['normal'],
                            bg=self.colors['gray_light'],
                            fg=self.colors['text_primary'],
                            activebackground=self.colors['accent'],
                            selectcolor=self.colors['primary'],
                            anchor="e", justify="right",
                            pady=15, cursor="hand2",
                            relief="flat", bd=0)
        btn.pack(fill="x", padx=8, pady=8)
        
        self.section_buttons[section_id] = btn
        
    def create_quick_tools(self, parent):
        """إنشاء الأدوات السريعة"""
        # فاصل
        tk.Frame(parent, bg=self.colors['accent'], height=3).pack(fill="x", pady=20)
        
        # رأس الأدوات
        tools_header = tk.Frame(parent, bg=self.colors['secondary'], height=50)
        tools_header.pack(fill="x")
        tools_header.pack_propagate(False)
        
        tk.Label(tools_header, text="⚡ أدوات سريعة",
                font=self.fonts['normal'], bg=self.colors['secondary'],
                fg=self.colors['accent'], anchor="e").pack(expand=True, padx=20)
        
        # أزرار الأدوات
        tools_data = [
            ("💾", "حفظ المشروع", self.save_project, self.colors['success']),
            ("📂", "فتح مشروع", self.load_project, self.colors['info']),
            ("📄", "تصدير PDF", self.export_pdf, self.colors['warning']),
            ("🔄", "مسح الكل", self.clear_all, self.colors['danger'])
        ]
        
        for icon, text, command, color in tools_data:
            self.create_tool_button(parent, icon, text, command, color)
            
    def create_tool_button(self, parent, icon, text, command, color):
        """إنشاء زر أداة"""
        btn_container = tk.Frame(parent, bg=self.colors['white'])
        btn_container.pack(fill="x", padx=15, pady=4)
        
        btn = tk.Button(btn_container, text=f"{icon} {text}",
                       command=command, font=self.fonts['normal'],
                       bg=color, fg=self.colors['white'],
                       relief="flat", bd=0, pady=12, cursor="hand2",
                       anchor="e", justify="right")
        btn.pack(fill="x", padx=5, pady=3)
        
    def create_content_area(self, parent):
        """إنشاء منطقة المحتوى"""
        self.content_frame = tk.Frame(parent, bg=self.colors['white'], relief="solid", bd=1)
        self.content_frame.pack(side="left", fill="both", expand=True)
        
        # رسالة ترحيب افتراضية
        welcome_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        welcome_frame.pack(expand=True, fill="both")
        
        tk.Label(welcome_frame, text="🏢 مرحباً بك في نظام تخطيط المشاريع",
                font=self.fonts['title'], bg=self.colors['white'],
                fg=self.colors['primary']).pack(expand=True)
        
        tk.Label(welcome_frame, text="اختر قسماً من القائمة الجانبية للبدء",
                font=self.fonts['normal'], bg=self.colors['white'],
                fg=self.colors['text_secondary']).pack()
                
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Frame(parent, bg=self.colors['gray_medium'], height=30)
        self.status_bar.pack(fill="x")
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_bar, text="جاهز",
                                    font=self.fonts['small'],
                                    bg=self.colors['gray_medium'],
                                    fg=self.colors['text_secondary'],
                                    anchor="e")
        self.status_label.pack(side="right", padx=20, pady=5)
        
    def show_section(self, section_id):
        """عرض قسم معين"""
        # مسح المحتوى الحالي
        for widget in self.content_frame.winfo_children():
            widget.destroy()
            
        # عرض القسم المطلوب
        try:
            section = None
            if section_id == "section1" and PersonalInfoSection:
                section = PersonalInfoSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section2" and MarketAnalysisSection:
                section = MarketAnalysisSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section3" and SwotAnalysisSection:
                section = SwotAnalysisSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section4" and MarketingMixSection:
                section = MarketingMixSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section5" and RequirementsSection:
                section = RequirementsSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section6" and StartupCostsSection:
                section = StartupCostsSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section7" and OperatingCostsSection:
                section = OperatingCostsSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section8" and FinancialSummarySection:
                section = FinancialSummarySection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section9" and AnnualRevenueSection:
                section = AnnualRevenueSection(self.content_frame, self.colors, self.fonts)
            elif section_id == "section10" and ProfitLossSection:
                section = ProfitLossSection(self.content_frame, self.colors, self.fonts)
            else:
                self.show_placeholder(section_id)
                return

            self.current_section = section
            self.update_status(f"تم تحميل قسم: {section_id}")

        except Exception as e:
            self.show_error_message(f"خطأ في تحميل القسم: {str(e)}")
            
    def show_placeholder(self, section_id):
        """عرض رسالة مؤقتة للأقسام غير المكتملة"""
        placeholder_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        placeholder_frame.pack(expand=True, fill="both")
        
        tk.Label(placeholder_frame, text=f"🚧 القسم {section_id} قيد التطوير",
                font=self.fonts['title'], bg=self.colors['white'],
                fg=self.colors['warning']).pack(expand=True)
                
    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        error_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        error_frame.pack(expand=True, fill="both")
        
        tk.Label(error_frame, text="❌ حدث خطأ",
                font=self.fonts['title'], bg=self.colors['white'],
                fg=self.colors['danger']).pack(expand=True)
                
        tk.Label(error_frame, text=message,
                font=self.fonts['normal'], bg=self.colors['white'],
                fg=self.colors['text_secondary']).pack()
                
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        
    def save_project(self):
        """حفظ المشروع"""
        try:
            # جمع البيانات من جميع الأقسام
            project_data = self.collect_all_data()
            self.data_manager.save_project(project_data)
            self.update_status("تم حفظ المشروع بنجاح")
            messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {str(e)}")
            
    def load_project(self):
        """تحميل مشروع"""
        try:
            project_data = self.data_manager.load_project()
            if project_data:
                self.load_all_data(project_data)
                self.update_status("تم تحميل المشروع بنجاح")
                messagebox.showinfo("نجح", "تم تحميل المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {str(e)}")
            
    def export_pdf(self):
        """تصدير إلى PDF"""
        try:
            from utils.pdf_exporter import PDFExporter
            exporter = PDFExporter()
            project_data = self.collect_all_data()
            exporter.export_to_pdf(project_data)
            self.update_status("تم تصدير PDF بنجاح")
            messagebox.showinfo("نجح", "تم تصدير PDF بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF: {str(e)}")
            
    def clear_all(self):
        """مسح جميع البيانات"""
        if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح جميع البيانات؟"):
            self.sections_data = {}
            if self.current_section:
                self.current_section.clear_data()
            self.update_status("تم مسح جميع البيانات")
            
    def collect_all_data(self):
        """جمع البيانات من جميع الأقسام"""
        # سيتم تنفيذها لاحقاً
        return self.sections_data
        
    def load_all_data(self, data):
        """تحميل البيانات إلى جميع الأقسام"""
        # سيتم تنفيذها لاحقاً
        self.sections_data = data
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = BusinessPlanApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل التطبيق: {str(e)}")
