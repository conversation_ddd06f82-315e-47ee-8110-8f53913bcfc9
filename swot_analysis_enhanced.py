#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل SWOT المحسّن
Enhanced SWOT Analysis
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
from datetime import datetime

class SWOTAnalysisApp:
    def __init__(self, root):
        self.root = root
        self.root.title("تحليل SWOT الشامل - خطة العمل")
        self.root.geometry("1200x900")
        self.root.configure(bg="#f5f7fa")

        # الخطوط والألوان
        self.label_font = ("Arial", 12, "bold")
        self.entry_font = ("Arial", 11)
        self.title_font = ("Arial", 14, "bold")
        self.box_bg = "#ffffff"

        # تخزين البيانات
        self.swot_data = {
            'strengths': [],
            'weaknesses': [],
            'opportunities': [],
            'threats': []
        }

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # شريط القوائم
        self.create_menu()

        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg="#2c3e50", height=60)
        title_frame.pack(fill="x", padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="📊 تحليل SWOT الشامل",
                              font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title_label.pack(expand=True)

        # دفتر التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء التبويبات
        self.create_swot_tab()
        self.create_analysis_tab()
        self.create_strategies_tab()

    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_analysis)
        file_menu.add_command(label="فتح", command=self.open_analysis)
        file_menu.add_command(label="حفظ", command=self.save_analysis)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير تقرير", command=self.export_report)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="مسح جميع البيانات", command=self.clear_all_data)
        tools_menu.add_command(label="إضافة أمثلة", command=self.add_examples)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)

    def create_swot_tab(self):
        """إنشاء تبويب تحليل SWOT الرئيسي"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="📈 تحليل SWOT")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إرشادات
        instructions_frame = tk.LabelFrame(scrollable_frame, text="📋 إرشادات التحليل",
                                         font=self.label_font, bg="#e8f4fd", relief="groove", bd=2)
        instructions_frame.pack(fill="x", padx=10, pady=10)

        instructions_text = """
        🔍 تحليل SWOT هو أداة استراتيجية لتقييم نقاط القوة والضعف والفرص والتهديدات

        💪 نقاط القوة: العوامل الداخلية الإيجابية (مهارات، موارد، خبرات، مزايا تنافسية)
        ⚠️ نقاط الضعف: العوامل الداخلية السلبية (نقص خبرة، موارد محدودة، مشاكل داخلية)
        🌟 الفرص: العوامل الخارجية الإيجابية (نمو السوق، تغيير القوانين، اتجاهات جديدة)
        ⚡ التهديدات: العوامل الخارجية السلبية (منافسة، تغيير اقتصادي، مخاطر السوق)
        """

        tk.Label(instructions_frame, text=instructions_text, font=("Arial", 10),
                bg="#e8f4fd", justify="right", wraplength=1000).pack(padx=10, pady=5)

        # إطار رئيسي للتحليل
        main_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # نقاط القوة
        self.strengths_entries = self.create_swot_section(
            main_frame, "💪 نقاط القوة (Strengths)", "#d1fae5", 0, 0
        )

        # نقاط الضعف
        self.weaknesses_entries = self.create_swot_section(
            main_frame, "⚠️ نقاط الضعف (Weaknesses)", "#fee2e2", 0, 1
        )

        # الفرص
        self.opportunities_entries = self.create_swot_section(
            main_frame, "🌟 الفرص (Opportunities)", "#e0f2fe", 1, 0
        )

        # التهديدات
        self.threats_entries = self.create_swot_section(
            main_frame, "⚡ التهديدات (Threats)", "#fef9c3", 1, 1
        )

        # تكوين الشبكة
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(buttons_frame, text="💾 حفظ التحليل", command=self.save_swot_data,
                 bg="#3498db", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="🔄 مسح البيانات", command=self.clear_swot_data,
                 bg="#e74c3c", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="📊 تحليل متقدم", command=self.advanced_analysis,
                 bg="#f39c12", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)

        tk.Button(buttons_frame, text="💡 أمثلة", command=self.add_examples,
                 bg="#9b59b6", fg="white", font=self.label_font, width=15).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_swot_section(self, parent, title, color, row, col):
        """إنشاء قسم SWOT"""
        frame = tk.LabelFrame(parent, text=title, bg=color, font=self.title_font,
                             fg="black", padx=10, pady=10, relief="groove", bd=2)
        frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

        # إطار للمدخلات
        entries_frame = tk.Frame(frame, bg=color)
        entries_frame.pack(fill="both", expand=True)

        entries = []
        for i in range(8):  # زيادة عدد الحقول إلى 8
            entry_frame = tk.Frame(entries_frame, bg=color)
            entry_frame.pack(fill="x", pady=2)

            # رقم النقطة
            tk.Label(entry_frame, text=f"{i+1}.", font=self.label_font,
                    bg=color, width=3).pack(side="left")

            # حقل الإدخال
            entry = tk.Entry(entry_frame, font=self.entry_font, width=50)
            entry.pack(side="left", fill="x", expand=True, padx=(5, 0))
            entries.append(entry)

        # زر إضافة المزيد
        add_button = tk.Button(frame, text="+ إضافة نقطة",
                              command=lambda: self.add_point(entries_frame, color, entries),
                              bg="white", font=("Arial", 9))
        add_button.pack(pady=5)

        return entries

    def add_point(self, parent_frame, color, entries_list):
        """إضافة نقطة جديدة"""
        entry_frame = tk.Frame(parent_frame, bg=color)
        entry_frame.pack(fill="x", pady=2)

        # رقم النقطة
        point_num = len(entries_list) + 1
        tk.Label(entry_frame, text=f"{point_num}.", font=self.label_font,
                bg=color, width=3).pack(side="left")

        # حقل الإدخال
        entry = tk.Entry(entry_frame, font=self.entry_font, width=50)
        entry.pack(side="left", fill="x", expand=True, padx=(5, 0))
        entries_list.append(entry)

    def create_analysis_tab(self):
        """إنشاء تبويب التحليل المتقدم"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="📊 التحليل المتقدم")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # إحصائيات التحليل
        stats_frame = tk.LabelFrame(scrollable_frame, text="📈 إحصائيات التحليل",
                                  font=self.label_font, bg="#e8f4fd", relief="groove", bd=2)
        stats_frame.pack(fill="x", padx=10, pady=10)

        # عدد النقاط
        self.stats_labels = {}
        stats_data = [
            ("نقاط القوة", "strengths", "#d1fae5"),
            ("نقاط الضعف", "weaknesses", "#fee2e2"),
            ("الفرص", "opportunities", "#e0f2fe"),
            ("التهديدات", "threats", "#fef9c3")
        ]

        for i, (name, key, color) in enumerate(stats_data):
            tk.Label(stats_frame, text=f"{name}:", font=self.label_font, bg="#e8f4fd").grid(
                row=i, column=0, sticky="e", padx=10, pady=5)
            label = tk.Label(stats_frame, text="0", font=self.label_font,
                           bg=color, relief="sunken", width=10)
            label.grid(row=i, column=1, padx=10, pady=5, sticky="w")
            self.stats_labels[key] = label

        # تحليل التوازن
        balance_frame = tk.LabelFrame(scrollable_frame, text="⚖️ تحليل التوازن",
                                    font=self.label_font, bg="#fff2e8", relief="groove", bd=2)
        balance_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(balance_frame, text="نسبة القوة إلى الضعف:", font=self.label_font, bg="#fff2e8").grid(
            row=0, column=0, sticky="e", padx=10, pady=5)
        self.strength_weakness_ratio = tk.Label(balance_frame, text="0:0", font=self.label_font,
                                              bg="white", relief="sunken", width=15)
        self.strength_weakness_ratio.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        tk.Label(balance_frame, text="نسبة الفرص إلى التهديدات:", font=self.label_font, bg="#fff2e8").grid(
            row=1, column=0, sticky="e", padx=10, pady=5)
        self.opportunity_threat_ratio = tk.Label(balance_frame, text="0:0", font=self.label_font,
                                               bg="white", relief="sunken", width=15)
        self.opportunity_threat_ratio.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # التوصيات
        recommendations_frame = tk.LabelFrame(scrollable_frame, text="💡 التوصيات الاستراتيجية",
                                            font=self.label_font, bg="#f0f9ff", relief="groove", bd=2)
        recommendations_frame.pack(fill="x", padx=10, pady=10)

        self.recommendations_text = tk.Text(recommendations_frame, height=8, font=self.entry_font,
                                          wrap=tk.WORD, bg="white")
        self.recommendations_text.pack(fill="x", padx=10, pady=10)

        # أزرار التحليل
        analysis_buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        analysis_buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(analysis_buttons_frame, text="🔄 تحديث الإحصائيات", command=self.update_statistics,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(analysis_buttons_frame, text="💡 إنشاء توصيات", command=self.generate_recommendations,
                 bg="#f39c12", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def create_strategies_tab(self):
        """إنشاء تبويب الاستراتيجيات"""
        tab = tk.Frame(self.notebook, bg="#f5f7fa")
        self.notebook.add(tab, text="🎯 الاستراتيجيات")

        # إطار قابل للتمرير
        canvas = tk.Canvas(tab, bg="#f5f7fa")
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#f5f7fa")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # مصفوفة الاستراتيجيات
        strategies_frame = tk.LabelFrame(scrollable_frame, text="🎯 مصفوفة الاستراتيجيات",
                                       font=self.label_font, bg="#f8f9fa", relief="groove", bd=2)
        strategies_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء الشبكة
        main_grid = tk.Frame(strategies_frame, bg="#f8f9fa")
        main_grid.pack(fill="both", expand=True, padx=10, pady=10)

        # استراتيجية SO (القوة + الفرص)
        so_frame = tk.LabelFrame(main_grid, text="💪🌟 استراتيجية SO (القوة + الفرص)",
                               font=self.label_font, bg="#d1fae5", relief="groove", bd=2)
        so_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        self.so_text = tk.Text(so_frame, height=6, font=self.entry_font, wrap=tk.WORD)
        self.so_text.pack(fill="both", expand=True, padx=5, pady=5)

        # استراتيجية WO (الضعف + الفرص)
        wo_frame = tk.LabelFrame(main_grid, text="⚠️🌟 استراتيجية WO (الضعف + الفرص)",
                               font=self.label_font, bg="#fee2e2", relief="groove", bd=2)
        wo_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        self.wo_text = tk.Text(wo_frame, height=6, font=self.entry_font, wrap=tk.WORD)
        self.wo_text.pack(fill="both", expand=True, padx=5, pady=5)

        # استراتيجية ST (القوة + التهديدات)
        st_frame = tk.LabelFrame(main_grid, text="💪⚡ استراتيجية ST (القوة + التهديدات)",
                               font=self.label_font, bg="#e0f2fe", relief="groove", bd=2)
        st_frame.grid(row=1, column=0, padx=5, pady=5, sticky="nsew")

        self.st_text = tk.Text(st_frame, height=6, font=self.entry_font, wrap=tk.WORD)
        self.st_text.pack(fill="both", expand=True, padx=5, pady=5)

        # استراتيجية WT (الضعف + التهديدات)
        wt_frame = tk.LabelFrame(main_grid, text="⚠️⚡ استراتيجية WT (الضعف + التهديدات)",
                               font=self.label_font, bg="#fef9c3", relief="groove", bd=2)
        wt_frame.grid(row=1, column=1, padx=5, pady=5, sticky="nsew")

        self.wt_text = tk.Text(wt_frame, height=6, font=self.entry_font, wrap=tk.WORD)
        self.wt_text.pack(fill="both", expand=True, padx=5, pady=5)

        # تكوين الشبكة
        main_grid.grid_rowconfigure(0, weight=1)
        main_grid.grid_rowconfigure(1, weight=1)
        main_grid.grid_columnconfigure(0, weight=1)
        main_grid.grid_columnconfigure(1, weight=1)

        # أزرار الاستراتيجيات
        strategies_buttons_frame = tk.Frame(scrollable_frame, bg="#f5f7fa")
        strategies_buttons_frame.pack(fill="x", padx=10, pady=10)

        tk.Button(strategies_buttons_frame, text="🎯 إنشاء استراتيجيات تلقائية",
                 command=self.generate_strategies,
                 bg="#3498db", fg="white", font=self.label_font).pack(side="left", padx=5)

        tk.Button(strategies_buttons_frame, text="🔄 مسح الاستراتيجيات",
                 command=self.clear_strategies,
                 bg="#e74c3c", fg="white", font=self.label_font).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        return tab

    def save_swot_data(self):
        """حفظ بيانات SWOT"""
        self.collect_swot_data()
        messagebox.showinfo("تم الحفظ", "تم حفظ بيانات تحليل SWOT بنجاح")

    def collect_swot_data(self):
        """جمع بيانات SWOT من الواجهة"""
        # جمع نقاط القوة
        self.swot_data['strengths'] = [entry.get().strip() for entry in self.strengths_entries
                                     if entry.get().strip()]

        # جمع نقاط الضعف
        self.swot_data['weaknesses'] = [entry.get().strip() for entry in self.weaknesses_entries
                                      if entry.get().strip()]

        # جمع الفرص
        self.swot_data['opportunities'] = [entry.get().strip() for entry in self.opportunities_entries
                                         if entry.get().strip()]

        # جمع التهديدات
        self.swot_data['threats'] = [entry.get().strip() for entry in self.threats_entries
                                   if entry.get().strip()]

    def clear_swot_data(self):
        """مسح بيانات SWOT"""
        result = messagebox.askyesno("تأكيد المسح", "هل تريد مسح جميع بيانات تحليل SWOT؟")
        if result:
            # مسح نقاط القوة
            for entry in self.strengths_entries:
                entry.delete(0, tk.END)

            # مسح نقاط الضعف
            for entry in self.weaknesses_entries:
                entry.delete(0, tk.END)

            # مسح الفرص
            for entry in self.opportunities_entries:
                entry.delete(0, tk.END)

            # مسح التهديدات
            for entry in self.threats_entries:
                entry.delete(0, tk.END)

            messagebox.showinfo("تم المسح", "تم مسح جميع البيانات بنجاح")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        self.collect_swot_data()

        # تحديث عدد النقاط
        self.stats_labels['strengths'].config(text=str(len(self.swot_data['strengths'])))
        self.stats_labels['weaknesses'].config(text=str(len(self.swot_data['weaknesses'])))
        self.stats_labels['opportunities'].config(text=str(len(self.swot_data['opportunities'])))
        self.stats_labels['threats'].config(text=str(len(self.swot_data['threats'])))

        # تحديث النسب
        strengths_count = len(self.swot_data['strengths'])
        weaknesses_count = len(self.swot_data['weaknesses'])
        opportunities_count = len(self.swot_data['opportunities'])
        threats_count = len(self.swot_data['threats'])

        self.strength_weakness_ratio.config(text=f"{strengths_count}:{weaknesses_count}")
        self.opportunity_threat_ratio.config(text=f"{opportunities_count}:{threats_count}")

    def generate_recommendations(self):
        """إنشاء توصيات استراتيجية"""
        self.collect_swot_data()

        recommendations = []

        # تحليل نقاط القوة
        if len(self.swot_data['strengths']) > len(self.swot_data['weaknesses']):
            recommendations.append("✅ لديك نقاط قوة أكثر من نقاط الضعف - استغل هذه القوة في التوسع")
        elif len(self.swot_data['weaknesses']) > len(self.swot_data['strengths']):
            recommendations.append("⚠️ تحتاج لتقوية نقاط الضعف - ركز على التحسين الداخلي")

        # تحليل الفرص والتهديدات
        if len(self.swot_data['opportunities']) > len(self.swot_data['threats']):
            recommendations.append("🌟 البيئة الخارجية مواتية - استغل الفرص المتاحة")
        elif len(self.swot_data['threats']) > len(self.swot_data['opportunities']):
            recommendations.append("⚡ احذر من التهديدات - ضع خطط للحماية والدفاع")

        # توصيات عامة
        if len(self.swot_data['strengths']) >= 3:
            recommendations.append("💪 استخدم نقاط القوة لاستغلال الفرص الجديدة")

        if len(self.swot_data['opportunities']) >= 3:
            recommendations.append("🚀 هناك فرص كثيرة - رتب أولوياتك واستغل الأهم")

        if len(self.swot_data['threats']) >= 3:
            recommendations.append("🛡️ التهديدات متعددة - ضع خطة إدارة مخاطر شاملة")

        # عرض التوصيات
        self.recommendations_text.delete(1.0, tk.END)
        if recommendations:
            self.recommendations_text.insert(1.0, "\n".join(recommendations))
        else:
            self.recommendations_text.insert(1.0, "يرجى إكمال تحليل SWOT أولاً للحصول على توصيات مخصصة")

    def generate_strategies(self):
        """إنشاء استراتيجيات تلقائية"""
        self.collect_swot_data()

        # استراتيجية SO
        so_strategy = "استراتيجيات استغلال القوة للاستفادة من الفرص:\n"
        if self.swot_data['strengths'] and self.swot_data['opportunities']:
            so_strategy += "• استخدم نقاط القوة الحالية لاستغلال الفرص المتاحة\n"
            so_strategy += "• طور منتجات جديدة بناءً على قوتك ومتطلبات السوق\n"
        self.so_text.delete(1.0, tk.END)
        self.so_text.insert(1.0, so_strategy)

        # استراتيجية WO
        wo_strategy = "استراتيجيات تحسين الضعف لاستغلال الفرص:\n"
        if self.swot_data['weaknesses'] and self.swot_data['opportunities']:
            wo_strategy += "• طور نقاط الضعف للاستفادة من الفرص\n"
            wo_strategy += "• ابحث عن شراكات لتعويض نقاط الضعف\n"
        self.wo_text.delete(1.0, tk.END)
        self.wo_text.insert(1.0, wo_strategy)

        # استراتيجية ST
        st_strategy = "استراتيجيات استخدام القوة لمواجهة التهديدات:\n"
        if self.swot_data['strengths'] and self.swot_data['threats']:
            st_strategy += "• استخدم نقاط القوة للدفاع ضد التهديدات\n"
            st_strategy += "• حول التهديدات إلى فرص باستخدام قوتك\n"
        self.st_text.delete(1.0, tk.END)
        self.st_text.insert(1.0, st_strategy)

        # استراتيجية WT
        wt_strategy = "استراتيجيات تقليل الضعف وتجنب التهديدات:\n"
        if self.swot_data['weaknesses'] and self.swot_data['threats']:
            wt_strategy += "• قلل من نقاط الضعف لتجنب التهديدات\n"
            wt_strategy += "• ضع خطط طوارئ للحالات الصعبة\n"
        self.wt_text.delete(1.0, tk.END)
        self.wt_text.insert(1.0, wt_strategy)

    def clear_strategies(self):
        """مسح الاستراتيجيات"""
        self.so_text.delete(1.0, tk.END)
        self.wo_text.delete(1.0, tk.END)
        self.st_text.delete(1.0, tk.END)
        self.wt_text.delete(1.0, tk.END)

    def advanced_analysis(self):
        """التحليل المتقدم"""
        self.update_statistics()
        self.generate_recommendations()
        # التبديل إلى تبويب التحليل المتقدم
        self.notebook.select(1)

    def add_examples(self):
        """إضافة أمثلة للتحليل"""
        result = messagebox.askyesno("إضافة أمثلة", "هل تريد إضافة أمثلة لتحليل SWOT؟\nسيتم مسح البيانات الحالية.")
        if result:
            # مسح البيانات الحالية
            self.clear_swot_data()

            # أمثلة نقاط القوة
            strengths_examples = [
                "فريق عمل ذو خبرة عالية",
                "منتجات عالية الجودة",
                "علاقات قوية مع العملاء",
                "موقع استراتيجي ممتاز"
            ]

            # أمثلة نقاط الضعف
            weaknesses_examples = [
                "رأس مال محدود",
                "نقص في الخبرة التسويقية",
                "اعتماد على مورد واحد",
                "نظام إدارة تقليدي"
            ]

            # أمثلة الفرص
            opportunities_examples = [
                "نمو السوق المحلي",
                "دعم حكومي للمشاريع الصغيرة",
                "تطور التكنولوجيا",
                "زيادة الطلب على المنتج"
            ]

            # أمثلة التهديدات
            threats_examples = [
                "منافسة شديدة من الشركات الكبيرة",
                "تقلبات اقتصادية",
                "تغيير في القوانين",
                "ارتفاع تكاليف المواد الخام"
            ]

            # إدخال الأمثلة
            for i, example in enumerate(strengths_examples):
                if i < len(self.strengths_entries):
                    self.strengths_entries[i].insert(0, example)

            for i, example in enumerate(weaknesses_examples):
                if i < len(self.weaknesses_entries):
                    self.weaknesses_entries[i].insert(0, example)

            for i, example in enumerate(opportunities_examples):
                if i < len(self.opportunities_entries):
                    self.opportunities_entries[i].insert(0, example)

            for i, example in enumerate(threats_examples):
                if i < len(self.threats_entries):
                    self.threats_entries[i].insert(0, example)

    def new_analysis(self):
        """تحليل جديد"""
        self.clear_all_data()

    def open_analysis(self):
        """فتح تحليل محفوظ"""
        filename = filedialog.askopenfilename(
            title="فتح تحليل SWOT",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.load_data(data)
                messagebox.showinfo("نجح", "تم فتح التحليل بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {str(e)}")

    def save_analysis(self):
        """حفظ التحليل"""
        filename = filedialog.asksaveasfilename(
            title="حفظ تحليل SWOT",
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
        )
        if filename:
            try:
                self.collect_swot_data()
                data = {
                    'swot_data': self.swot_data,
                    'timestamp': datetime.now().isoformat()
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("نجح", "تم حفظ التحليل بنجاح")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")

    def load_data(self, data):
        """تحميل البيانات"""
        if 'swot_data' in data:
            swot_data = data['swot_data']

            # تحميل نقاط القوة
            for i, strength in enumerate(swot_data.get('strengths', [])):
                if i < len(self.strengths_entries):
                    self.strengths_entries[i].delete(0, tk.END)
                    self.strengths_entries[i].insert(0, strength)

            # تحميل نقاط الضعف
            for i, weakness in enumerate(swot_data.get('weaknesses', [])):
                if i < len(self.weaknesses_entries):
                    self.weaknesses_entries[i].delete(0, tk.END)
                    self.weaknesses_entries[i].insert(0, weakness)

            # تحميل الفرص
            for i, opportunity in enumerate(swot_data.get('opportunities', [])):
                if i < len(self.opportunities_entries):
                    self.opportunities_entries[i].delete(0, tk.END)
                    self.opportunities_entries[i].insert(0, opportunity)

            # تحميل التهديدات
            for i, threat in enumerate(swot_data.get('threats', [])):
                if i < len(self.threats_entries):
                    self.threats_entries[i].delete(0, tk.END)
                    self.threats_entries[i].insert(0, threat)

    def export_report(self):
        """تصدير تقرير"""
        messagebox.showinfo("قريباً", "ميزة تصدير التقرير ستكون متاحة قريباً")

    def clear_all_data(self):
        """مسح جميع البيانات"""
        self.clear_swot_data()
        self.clear_strategies()

        # مسح التوصيات
        self.recommendations_text.delete(1.0, tk.END)

        # إعادة تعيين الإحصائيات
        for label in self.stats_labels.values():
            label.config(text="0")
        self.strength_weakness_ratio.config(text="0:0")
        self.opportunity_threat_ratio.config(text="0:0")

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        📋 دليل استخدام تطبيق تحليل SWOT

        🔍 ما هو تحليل SWOT؟
        تحليل SWOT هو أداة استراتيجية لتقييم:
        • نقاط القوة (Strengths)
        • نقاط الضعف (Weaknesses)
        • الفرص (Opportunities)
        • التهديدات (Threats)

        📝 كيفية الاستخدام:
        1. املأ كل قسم بالنقاط المناسبة
        2. استخدم التحليل المتقدم للإحصائيات
        3. راجع التوصيات المقترحة
        4. طور الاستراتيجيات المناسبة

        💾 الحفظ والتحميل:
        • احفظ تحليلك في ملف JSON
        • حمّل تحليلات سابقة
        • صدّر تقارير (قريباً)
        """
        messagebox.showinfo("دليل الاستخدام", help_text)

    def show_about(self):
        """عرض معلومات البرنامج"""
        messagebox.showinfo("حول البرنامج",
                           "تطبيق تحليل SWOT الشامل\nالإصدار 1.0\n\nأداة متقدمة لتحليل SWOT وإنشاء الاستراتيجيات")

if __name__ == "__main__":
    root = tk.Tk()
    app = SWOTAnalysisApp(root)
    root.mainloop()