#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk

class AnnualRevenueSection:
    def __init__(self, parent, colors, fonts):
        placeholder_frame = tk.Frame(parent, bg=colors['white'])
        placeholder_frame.pack(expand=True, fill="both")
        tk.Label(placeholder_frame, text="💵 الإيرادات السنوية", font=fonts['title'], 
                bg=colors['white'], fg=colors['primary']).pack(expand=True)
                
    def get_data(self): return {}
    def set_data(self, data): pass
    def clear_data(self): pass
